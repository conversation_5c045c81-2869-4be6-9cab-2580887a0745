{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nexports.default = function (instance, callback) {\n  var speed = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 300;\n  var once = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (!instance || !callback) throw new Error('instance & callback is required');\n  var called = false;\n  var afterLeaveCallback = function afterLeaveCallback() {\n    if (called) return;\n    called = true;\n    if (callback) {\n      callback.apply(null, arguments);\n    }\n  };\n  if (once) {\n    instance.$once('after-leave', afterLeaveCallback);\n  } else {\n    instance.$on('after-leave', afterLeaveCallback);\n  }\n  setTimeout(function () {\n    afterLeaveCallback();\n  }, speed + 100);\n};\n; /**\n   * Bind after-leave event for vue instance. Make sure after-leave is called in any browsers.\n   *\n   * @param {Vue} instance Vue instance.\n   * @param {Function} callback callback of after-leave event\n   * @param {Number} speed the speed of transition, default value is 300ms\n   * @param {Boolean} once weather bind after-leave once. default value is false.\n   */", "map": {"version": 3, "names": ["exports", "__esModule", "default", "instance", "callback", "speed", "arguments", "length", "undefined", "once", "Error", "called", "afterLeaveCallback", "apply", "$once", "$on", "setTimeout"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/element-ui/lib/utils/after-leave.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nexports.default = function (instance, callback) {\n  var speed = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 300;\n  var once = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n\n  if (!instance || !callback) throw new Error('instance & callback is required');\n  var called = false;\n  var afterLeaveCallback = function afterLeaveCallback() {\n    if (called) return;\n    called = true;\n    if (callback) {\n      callback.apply(null, arguments);\n    }\n  };\n  if (once) {\n    instance.$once('after-leave', afterLeaveCallback);\n  } else {\n    instance.$on('after-leave', afterLeaveCallback);\n  }\n  setTimeout(function () {\n    afterLeaveCallback();\n  }, speed + 100);\n};\n\n; /**\n   * Bind after-leave event for vue instance. Make sure after-leave is called in any browsers.\n   *\n   * @param {Vue} instance Vue instance.\n   * @param {Function} callback callback of after-leave event\n   * @param {Number} speed the speed of transition, default value is 300ms\n   * @param {Boolean} once weather bind after-leave once. default value is false.\n   */"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzBD,OAAO,CAACE,OAAO,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EACnF,IAAIG,IAAI,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAEpF,IAAI,CAACH,QAAQ,IAAI,CAACC,QAAQ,EAAE,MAAM,IAAIM,KAAK,CAAC,iCAAiC,CAAC;EAC9E,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAID,MAAM,EAAE;IACZA,MAAM,GAAG,IAAI;IACb,IAAIP,QAAQ,EAAE;MACZA,QAAQ,CAACS,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;IACjC;EACF,CAAC;EACD,IAAIG,IAAI,EAAE;IACRN,QAAQ,CAACW,KAAK,CAAC,aAAa,EAAEF,kBAAkB,CAAC;EACnD,CAAC,MAAM;IACLT,QAAQ,CAACY,GAAG,CAAC,aAAa,EAAEH,kBAAkB,CAAC;EACjD;EACAI,UAAU,CAAC,YAAY;IACrBJ,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAEP,KAAK,GAAG,GAAG,CAAC;AACjB,CAAC;AAED,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}