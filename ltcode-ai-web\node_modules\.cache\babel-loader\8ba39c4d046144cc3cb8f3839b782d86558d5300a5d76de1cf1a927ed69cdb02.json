{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as util from '../util';\n\n/**\n *  Rule for validating required fields.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction required(rule, value, source, errors, options, type) {\n  if (rule.required && (!source.hasOwnProperty(rule.field) || util.isEmptyValue(value, type || rule.type))) {\n    errors.push(util.format(options.messages.required, rule.fullField));\n  }\n}\nexport default required;", "map": {"version": 3, "names": ["util", "required", "rule", "value", "source", "errors", "options", "type", "hasOwnProperty", "field", "isEmptyValue", "push", "format", "messages", "fullField"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/async-validator/es/rule/required.js"], "sourcesContent": ["import * as util from '../util';\n\n/**\n *  Rule for validating required fields.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction required(rule, value, source, errors, options, type) {\n  if (rule.required && (!source.hasOwnProperty(rule.field) || util.isEmptyValue(value, type || rule.type))) {\n    errors.push(util.format(options.messages.required, rule.fullField));\n  }\n}\n\nexport default required;"], "mappings": ";AAAA,OAAO,KAAKA,IAAI,MAAM,SAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAC5D,IAAIL,IAAI,CAACD,QAAQ,KAAK,CAACG,MAAM,CAACI,cAAc,CAACN,IAAI,CAACO,KAAK,CAAC,IAAIT,IAAI,CAACU,YAAY,CAACP,KAAK,EAAEI,IAAI,IAAIL,IAAI,CAACK,IAAI,CAAC,CAAC,EAAE;IACxGF,MAAM,CAACM,IAAI,CAACX,IAAI,CAACY,MAAM,CAACN,OAAO,CAACO,QAAQ,CAACZ,QAAQ,EAAEC,IAAI,CAACY,SAAS,CAAC,CAAC;EACrE;AACF;AAEA,eAAeb,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}