{"name": "ltcode-ai-web", "version": "1.0.0", "description": "Vue2 chat interface for ltcode AI module", "main": "index.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.6.0", "core-js": "^3.45.0", "element-ui": "^2.15.14", "vue": "^2.6.14", "vue-router": "^3.5.4"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "^7.28.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-service": "^5.0.8", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}