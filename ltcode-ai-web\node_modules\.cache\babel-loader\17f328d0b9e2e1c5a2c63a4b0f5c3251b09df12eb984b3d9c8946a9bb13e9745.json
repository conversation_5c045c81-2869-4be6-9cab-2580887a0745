{"ast": null, "code": "module.exports = {\n  \"default\": require(\"core-js/library/fn/symbol/iterator\"),\n  __esModule: true\n};", "map": {"version": 3, "names": ["module", "exports", "require", "__esModule"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/babel-runtime/core-js/symbol/iterator.js"], "sourcesContent": ["module.exports = { \"default\": require(\"core-js/library/fn/symbol/iterator\"), __esModule: true };"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EAAE,SAAS,EAAEC,OAAO,CAAC,oCAAoC,CAAC;EAAEC,UAAU,EAAE;AAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}