{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nimport './assets/css/global.css';\nVue.use(ElementUI);\nVue.config.productionTip = false;\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "ElementUI", "use", "config", "productionTip", "render", "h", "$mount"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\nimport './assets/css/global.css'\n\nVue.use(ElementUI)\n\nVue.config.productionTip = false\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,OAAO,yBAAyB;AAEhCH,GAAG,CAACI,GAAG,CAACD,SAAS,CAAC;AAElBH,GAAG,CAACK,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIN,GAAG,CAAC;EACNE,MAAM;EACNK,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACP,GAAG;AACpB,CAAC,CAAC,CAACQ,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}