{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"chat-container\"\n  }, [_vm._m(0), _c(\"div\", {\n    ref: \"messagesContainer\",\n    staticClass: \"chat-messages\"\n  }, _vm._l(_vm.messages, function (message, index) {\n    return _c(\"div\", {\n      key: index,\n      class: [\"message-item\", message.type]\n    }, [_c(\"div\", {\n      staticClass: \"message-content\"\n    }, [message.type === \"user\" && message.file ? _c(\"div\", {\n      staticClass: \"file-info\"\n    }, [_c(\"i\", {\n      staticClass: \"el-icon-picture\"\n    }), _vm._v(\" \" + _vm._s(message.file.name) + \" \")]) : _vm._e(), _c(\"div\", {\n      domProps: {\n        innerHTML: _vm._s(_vm.formatMessage(message.content))\n      }\n    }), message.loading ? _c(\"div\", {\n      staticClass: \"loading\"\n    }, [_c(\"i\", {\n      staticClass: \"el-icon-loading\"\n    }), _vm._v(\" 正在思考... \")]) : _vm._e()])]);\n  }), 0), _c(\"div\", {\n    staticClass: \"chat-input-area\"\n  }, [_c(\"div\", {\n    staticClass: \"api-selector\"\n  }, [_c(\"el-select\", {\n    attrs: {\n      placeholder: \"选择API接口\",\n      size: \"small\"\n    },\n    on: {\n      change: _vm.onApiChange\n    },\n    model: {\n      value: _vm.selectedApi,\n      callback: function ($$v) {\n        _vm.selectedApi = $$v;\n      },\n      expression: \"selectedApi\"\n    }\n  }, [_c(\"el-option-group\", {\n    attrs: {\n      label: \"文本聊天接口\"\n    }\n  }, _vm._l(_vm.textApis, function (api) {\n    return _c(\"el-option\", {\n      key: api.value,\n      attrs: {\n        label: api.label,\n        value: api.value\n      }\n    });\n  }), 1), _c(\"el-option-group\", {\n    attrs: {\n      label: \"多模态接口\"\n    }\n  }, _vm._l(_vm.multimodalApis, function (api) {\n    return _c(\"el-option\", {\n      key: api.value,\n      attrs: {\n        label: api.label,\n        value: api.value\n      }\n    });\n  }), 1)], 1)], 1), _vm.needsFile ? _c(\"div\", {\n    staticClass: \"file-upload\"\n  }, [_c(\"el-upload\", {\n    ref: \"upload\",\n    attrs: {\n      \"auto-upload\": false,\n      \"show-file-list\": true,\n      \"on-change\": _vm.handleFileChange,\n      \"before-remove\": _vm.handleFileRemove,\n      accept: \"image/*\",\n      drag: \"\"\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-upload\"\n  }), _c(\"div\", {\n    staticClass: \"el-upload__text\"\n  }, [_vm._v(\"将图片拖到此处，或\"), _c(\"em\", [_vm._v(\"点击上传\")])]), _c(\"div\", {\n    staticClass: \"el-upload__tip\",\n    attrs: {\n      slot: \"tip\"\n    },\n    slot: \"tip\"\n  }, [_vm._v(\"只能上传jpg/png文件，且不超过10MB\")])])], 1) : _vm._e(), _c(\"div\", {\n    staticClass: \"input-container\"\n  }, [_c(\"el-input\", {\n    attrs: {\n      type: \"textarea\",\n      rows: 3,\n      placeholder: \"请输入您的消息...\",\n      disabled: _vm.loading\n    },\n    on: {\n      keydown: function ($event) {\n        if (!$event.type.indexOf(\"key\") && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) return null;\n        if (!$event.ctrlKey) return null;\n        return _vm.sendMessage.apply(null, arguments);\n      }\n    },\n    model: {\n      value: _vm.inputMessage,\n      callback: function ($$v) {\n        _vm.inputMessage = $$v;\n      },\n      expression: \"inputMessage\"\n    }\n  }), _c(\"el-button\", {\n    key: _vm.enter,\n    attrs: {\n      type: \"primary\",\n      loading: _vm.loading,\n      disabled: !_vm.canSend,\n      size: \"large\"\n    },\n    on: {\n      click: _vm.sendMessage\n    }\n  }, [_vm._v(\" 发送 \")])], 1)])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"chat-header\"\n  }, [_c(\"h1\", [_vm._v(\"LTCode AI Chat\")]), _c(\"p\", [_vm._v(\"智能对话助手\")])]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_m", "ref", "_l", "messages", "message", "index", "key", "class", "type", "file", "_v", "_s", "name", "_e", "domProps", "innerHTML", "formatMessage", "content", "loading", "attrs", "placeholder", "size", "on", "change", "onApiChange", "model", "value", "<PERSON><PERSON><PERSON>", "callback", "$$v", "expression", "label", "textApis", "api", "multimodalApis", "needsFile", "handleFileChange", "handleFileRemove", "accept", "drag", "slot", "rows", "disabled", "keydown", "$event", "indexOf", "_k", "keyCode", "ctrl<PERSON>ey", "sendMessage", "apply", "arguments", "inputMessage", "enter", "canSend", "click", "staticRenderFns", "_withStripped"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/src/views/ChatView.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"chat-container\" }, [\n    _vm._m(0),\n    _c(\n      \"div\",\n      { ref: \"messagesContainer\", staticClass: \"chat-messages\" },\n      _vm._l(_vm.messages, function (message, index) {\n        return _c(\n          \"div\",\n          { key: index, class: [\"message-item\", message.type] },\n          [\n            _c(\"div\", { staticClass: \"message-content\" }, [\n              message.type === \"user\" && message.file\n                ? _c(\"div\", { staticClass: \"file-info\" }, [\n                    _c(\"i\", { staticClass: \"el-icon-picture\" }),\n                    _vm._v(\" \" + _vm._s(message.file.name) + \" \"),\n                  ])\n                : _vm._e(),\n              _c(\"div\", {\n                domProps: {\n                  innerHTML: _vm._s(_vm.formatMessage(message.content)),\n                },\n              }),\n              message.loading\n                ? _c(\"div\", { staticClass: \"loading\" }, [\n                    _c(\"i\", { staticClass: \"el-icon-loading\" }),\n                    _vm._v(\" 正在思考... \"),\n                  ])\n                : _vm._e(),\n            ]),\n          ]\n        )\n      }),\n      0\n    ),\n    _c(\"div\", { staticClass: \"chat-input-area\" }, [\n      _c(\n        \"div\",\n        { staticClass: \"api-selector\" },\n        [\n          _c(\n            \"el-select\",\n            {\n              attrs: { placeholder: \"选择API接口\", size: \"small\" },\n              on: { change: _vm.onApiChange },\n              model: {\n                value: _vm.selectedApi,\n                callback: function ($$v) {\n                  _vm.selectedApi = $$v\n                },\n                expression: \"selectedApi\",\n              },\n            },\n            [\n              _c(\n                \"el-option-group\",\n                { attrs: { label: \"文本聊天接口\" } },\n                _vm._l(_vm.textApis, function (api) {\n                  return _c(\"el-option\", {\n                    key: api.value,\n                    attrs: { label: api.label, value: api.value },\n                  })\n                }),\n                1\n              ),\n              _c(\n                \"el-option-group\",\n                { attrs: { label: \"多模态接口\" } },\n                _vm._l(_vm.multimodalApis, function (api) {\n                  return _c(\"el-option\", {\n                    key: api.value,\n                    attrs: { label: api.label, value: api.value },\n                  })\n                }),\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm.needsFile\n        ? _c(\n            \"div\",\n            { staticClass: \"file-upload\" },\n            [\n              _c(\n                \"el-upload\",\n                {\n                  ref: \"upload\",\n                  attrs: {\n                    \"auto-upload\": false,\n                    \"show-file-list\": true,\n                    \"on-change\": _vm.handleFileChange,\n                    \"before-remove\": _vm.handleFileRemove,\n                    accept: \"image/*\",\n                    drag: \"\",\n                  },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-upload\" }),\n                  _c(\"div\", { staticClass: \"el-upload__text\" }, [\n                    _vm._v(\"将图片拖到此处，或\"),\n                    _c(\"em\", [_vm._v(\"点击上传\")]),\n                  ]),\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"el-upload__tip\",\n                      attrs: { slot: \"tip\" },\n                      slot: \"tip\",\n                    },\n                    [_vm._v(\"只能上传jpg/png文件，且不超过10MB\")]\n                  ),\n                ]\n              ),\n            ],\n            1\n          )\n        : _vm._e(),\n      _c(\n        \"div\",\n        { staticClass: \"input-container\" },\n        [\n          _c(\"el-input\", {\n            attrs: {\n              type: \"textarea\",\n              rows: 3,\n              placeholder: \"请输入您的消息...\",\n              disabled: _vm.loading,\n            },\n            on: {\n              keydown: function ($event) {\n                if (\n                  !$event.type.indexOf(\"key\") &&\n                  _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                )\n                  return null\n                if (!$event.ctrlKey) return null\n                return _vm.sendMessage.apply(null, arguments)\n              },\n            },\n            model: {\n              value: _vm.inputMessage,\n              callback: function ($$v) {\n                _vm.inputMessage = $$v\n              },\n              expression: \"inputMessage\",\n            },\n          }),\n          _c(\n            \"el-button\",\n            {\n              key: _vm.enter,\n              attrs: {\n                type: \"primary\",\n                loading: _vm.loading,\n                disabled: !_vm.canSend,\n                size: \"large\",\n              },\n              on: { click: _vm.sendMessage },\n            },\n            [_vm._v(\" 发送 \")]\n          ),\n        ],\n        1\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"chat-header\" }, [\n      _c(\"h1\", [_vm._v(\"LTCode AI Chat\")]),\n      _c(\"p\", [_vm._v(\"智能对话助手\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDH,GAAG,CAACI,EAAE,CAAC,CAAC,CAAC,EACTH,EAAE,CACA,KAAK,EACL;IAAEI,GAAG,EAAE,mBAAmB;IAAEF,WAAW,EAAE;EAAgB,CAAC,EAC1DH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,QAAQ,EAAE,UAAUC,OAAO,EAAEC,KAAK,EAAE;IAC7C,OAAOR,EAAE,CACP,KAAK,EACL;MAAES,GAAG,EAAED,KAAK;MAAEE,KAAK,EAAE,CAAC,cAAc,EAAEH,OAAO,CAACI,IAAI;IAAE,CAAC,EACrD,CACEX,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,EAAE,CAC5CK,OAAO,CAACI,IAAI,KAAK,MAAM,IAAIJ,OAAO,CAACK,IAAI,GACnCZ,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,CAAC,EAC3CH,GAAG,CAACc,EAAE,CAAC,GAAG,GAAGd,GAAG,CAACe,EAAE,CAACP,OAAO,CAACK,IAAI,CAACG,IAAI,CAAC,GAAG,GAAG,CAAC,CAC9C,CAAC,GACFhB,GAAG,CAACiB,EAAE,CAAC,CAAC,EACZhB,EAAE,CAAC,KAAK,EAAE;MACRiB,QAAQ,EAAE;QACRC,SAAS,EAAEnB,GAAG,CAACe,EAAE,CAACf,GAAG,CAACoB,aAAa,CAACZ,OAAO,CAACa,OAAO,CAAC;MACtD;IACF,CAAC,CAAC,EACFb,OAAO,CAACc,OAAO,GACXrB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAU,CAAC,EAAE,CACpCF,EAAE,CAAC,GAAG,EAAE;MAAEE,WAAW,EAAE;IAAkB,CAAC,CAAC,EAC3CH,GAAG,CAACc,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,GACFd,GAAG,CAACiB,EAAE,CAAC,CAAC,CACb,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACDhB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;IACEsB,KAAK,EAAE;MAAEC,WAAW,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IAChDC,EAAE,EAAE;MAAEC,MAAM,EAAE3B,GAAG,CAAC4B;IAAY,CAAC;IAC/BC,KAAK,EAAE;MACLC,KAAK,EAAE9B,GAAG,CAAC+B,WAAW;MACtBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBjC,GAAG,CAAC+B,WAAW,GAAGE,GAAG;MACvB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEjC,EAAE,CACA,iBAAiB,EACjB;IAAEsB,KAAK,EAAE;MAAEY,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9BnC,GAAG,CAACM,EAAE,CAACN,GAAG,CAACoC,QAAQ,EAAE,UAAUC,GAAG,EAAE;IAClC,OAAOpC,EAAE,CAAC,WAAW,EAAE;MACrBS,GAAG,EAAE2B,GAAG,CAACP,KAAK;MACdP,KAAK,EAAE;QAAEY,KAAK,EAAEE,GAAG,CAACF,KAAK;QAAEL,KAAK,EAAEO,GAAG,CAACP;MAAM;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,EACD7B,EAAE,CACA,iBAAiB,EACjB;IAAEsB,KAAK,EAAE;MAAEY,KAAK,EAAE;IAAQ;EAAE,CAAC,EAC7BnC,GAAG,CAACM,EAAE,CAACN,GAAG,CAACsC,cAAc,EAAE,UAAUD,GAAG,EAAE;IACxC,OAAOpC,EAAE,CAAC,WAAW,EAAE;MACrBS,GAAG,EAAE2B,GAAG,CAACP,KAAK;MACdP,KAAK,EAAE;QAAEY,KAAK,EAAEE,GAAG,CAACF,KAAK;QAAEL,KAAK,EAAEO,GAAG,CAACP;MAAM;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACD9B,GAAG,CAACuC,SAAS,GACTtC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,WAAW,EACX;IACEI,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE;MACL,aAAa,EAAE,KAAK;MACpB,gBAAgB,EAAE,IAAI;MACtB,WAAW,EAAEvB,GAAG,CAACwC,gBAAgB;MACjC,eAAe,EAAExC,GAAG,CAACyC,gBAAgB;MACrCC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE;IACR;EACF,CAAC,EACD,CACE1C,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,EAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CH,GAAG,CAACc,EAAE,CAAC,WAAW,CAAC,EACnBb,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAC3B,CAAC,EACFb,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,gBAAgB;IAC7BoB,KAAK,EAAE;MAAEqB,IAAI,EAAE;IAAM,CAAC;IACtBA,IAAI,EAAE;EACR,CAAC,EACD,CAAC5C,GAAG,CAACc,EAAE,CAAC,wBAAwB,CAAC,CACnC,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,GACDd,GAAG,CAACiB,EAAE,CAAC,CAAC,EACZhB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAClC,CACEF,EAAE,CAAC,UAAU,EAAE;IACbsB,KAAK,EAAE;MACLX,IAAI,EAAE,UAAU;MAChBiC,IAAI,EAAE,CAAC;MACPrB,WAAW,EAAE,YAAY;MACzBsB,QAAQ,EAAE9C,GAAG,CAACsB;IAChB,CAAC;IACDI,EAAE,EAAE;MACFqB,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACzB,IACE,CAACA,MAAM,CAACpC,IAAI,CAACqC,OAAO,CAAC,KAAK,CAAC,IAC3BjD,GAAG,CAACkD,EAAE,CAACF,MAAM,CAACG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAEH,MAAM,CAACtC,GAAG,EAAE,OAAO,CAAC,EAExD,OAAO,IAAI;QACb,IAAI,CAACsC,MAAM,CAACI,OAAO,EAAE,OAAO,IAAI;QAChC,OAAOpD,GAAG,CAACqD,WAAW,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC/C;IACF,CAAC;IACD1B,KAAK,EAAE;MACLC,KAAK,EAAE9B,GAAG,CAACwD,YAAY;MACvBxB,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBjC,GAAG,CAACwD,YAAY,GAAGvB,GAAG;MACxB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACFjC,EAAE,CACA,WAAW,EACX;IACES,GAAG,EAAEV,GAAG,CAACyD,KAAK;IACdlC,KAAK,EAAE;MACLX,IAAI,EAAE,SAAS;MACfU,OAAO,EAAEtB,GAAG,CAACsB,OAAO;MACpBwB,QAAQ,EAAE,CAAC9C,GAAG,CAAC0D,OAAO;MACtBjC,IAAI,EAAE;IACR,CAAC;IACDC,EAAE,EAAE;MAAEiC,KAAK,EAAE3D,GAAG,CAACqD;IAAY;EAC/B,CAAC,EACD,CAACrD,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAI8C,eAAe,GAAG,CACpB,YAAY;EACV,IAAI5D,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACpCb,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC5B,CAAC;AACJ,CAAC,CACF;AACDf,MAAM,CAAC8D,aAAa,GAAG,IAAI;AAE3B,SAAS9D,MAAM,EAAE6D,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}