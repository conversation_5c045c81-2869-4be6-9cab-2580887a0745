package com.ltcode.controller;

import com.ltcode.assistant.ChatAssistant;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.output.TokenUsage;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author： ltcode
 * @Create by： 2025/8/5 0005 22:04
 * @ClassName: ChatDemoController
 * @Describe： 文本聊天模型
 */
@RestController
@RequestMapping("/chat")
public class ChatDemoController {

    @Resource(name = "qwen")
    private ChatModel chatModel;

    @GetMapping("/simple/demo/{msg}")
    public String simpleChat(@PathVariable("msg") String msg) {
        return chatModel.chat(msg);
    }

    /**
     * 采用底层charModel 进行个性化编码
     * @param msg
     * @return
     */
    @GetMapping("/substrate/demo/{msg}")
    public String substrateChat(@PathVariable("msg") String msg) {

        ChatResponse chatResponse = chatModel.chat(UserMessage.from(msg));
        TokenUsage tokenUsage = chatResponse.tokenUsage();
        System.out.println("tokenUsage = " + tokenUsage);

        return chatResponse.aiMessage().text();
    }

    @Resource(name = "chatAssistant")
    private ChatAssistant chatAssistant;

    @Resource(name = "streamChatAssistant")
    private ChatAssistant streamChatAssistant;

    /**
     * 采用AiServices 将自定义接口 交给ChatModel 进行处理
     * @param msg
     * @from https://docs.langchain4j.dev/tutorials/ai-services
     * @return
     */
    @GetMapping("/assistant/demo/{msg}")
    public String assistantChat(@PathVariable("msg") String msg) {
        return chatAssistant.chat(msg);
    }

    /**
     * 采用AiServices 将自定义接口 交给ChatModel 进行处理
     * @param msg
     * @from https://docs.langchain4j.dev/tutorials/ai-services
     * @return
     */
    @GetMapping("/assistantSystem/demo/{msg}")
    public String assistantWithSystemChat(@PathVariable("msg") String msg) {
        return chatAssistant.chatWithSystemMessage(msg);
    }

    /**
     * 响应式处理
     * @param msg
     * @return
     */
    @GetMapping(value = "/assistantStream/demo/{msg}" )
    public Flux<String> assistantStream(@PathVariable("msg") String msg) {
        Flux<String> flux = streamChatAssistant.assistantStream(msg);
        return flux.doOnNext(data -> {
            System.out.println("发送数据: " + data);
        }).doOnComplete(() -> {
            System.out.println("流完成");
        }).doOnError(error -> {
            System.err.println("流错误: " + error.getMessage());
        });
    }
}
