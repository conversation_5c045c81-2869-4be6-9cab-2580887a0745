{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\n/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\n/* eslint-disable require-jsdoc, valid-jsdoc */\nvar MapShim = function () {\n  if (typeof Map !== 'undefined') {\n    return Map;\n  }\n  /**\r\n   * Returns index in provided array that matches the specified key.\r\n   *\r\n   * @param {Array<Array>} arr\r\n   * @param {*} key\r\n   * @returns {number}\r\n   */\n  function getIndex(arr, key) {\n    var result = -1;\n    arr.some(function (entry, index) {\n      if (entry[0] === key) {\n        result = index;\n        return true;\n      }\n      return false;\n    });\n    return result;\n  }\n  return /** @class */function () {\n    function class_1() {\n      this.__entries__ = [];\n    }\n    Object.defineProperty(class_1.prototype, \"size\", {\n      /**\r\n       * @returns {boolean}\r\n       */\n      get: function () {\n        return this.__entries__.length;\n      },\n      enumerable: true,\n      configurable: true\n    });\n    /**\r\n     * @param {*} key\r\n     * @returns {*}\r\n     */\n    class_1.prototype.get = function (key) {\n      var index = getIndex(this.__entries__, key);\n      var entry = this.__entries__[index];\n      return entry && entry[1];\n    };\n    /**\r\n     * @param {*} key\r\n     * @param {*} value\r\n     * @returns {void}\r\n     */\n    class_1.prototype.set = function (key, value) {\n      var index = getIndex(this.__entries__, key);\n      if (~index) {\n        this.__entries__[index][1] = value;\n      } else {\n        this.__entries__.push([key, value]);\n      }\n    };\n    /**\r\n     * @param {*} key\r\n     * @returns {void}\r\n     */\n    class_1.prototype.delete = function (key) {\n      var entries = this.__entries__;\n      var index = getIndex(entries, key);\n      if (~index) {\n        entries.splice(index, 1);\n      }\n    };\n    /**\r\n     * @param {*} key\r\n     * @returns {void}\r\n     */\n    class_1.prototype.has = function (key) {\n      return !!~getIndex(this.__entries__, key);\n    };\n    /**\r\n     * @returns {void}\r\n     */\n    class_1.prototype.clear = function () {\n      this.__entries__.splice(0);\n    };\n    /**\r\n     * @param {Function} callback\r\n     * @param {*} [ctx=null]\r\n     * @returns {void}\r\n     */\n    class_1.prototype.forEach = function (callback, ctx) {\n      if (ctx === void 0) {\n        ctx = null;\n      }\n      for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\n        var entry = _a[_i];\n        callback.call(ctx, entry[1], entry[0]);\n      }\n    };\n    return class_1;\n  }();\n}();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\nvar global$1 = function () {\n  if (typeof global !== 'undefined' && global.Math === Math) {\n    return global;\n  }\n  if (typeof self !== 'undefined' && self.Math === Math) {\n    return self;\n  }\n  if (typeof window !== 'undefined' && window.Math === Math) {\n    return window;\n  }\n  // eslint-disable-next-line no-new-func\n  return Function('return this')();\n}();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\nvar requestAnimationFrame$1 = function () {\n  if (typeof requestAnimationFrame === 'function') {\n    // It's required to use a bounded function because IE sometimes throws\n    // an \"Invalid calling object\" error if rAF is invoked without the global\n    // object on the left hand side.\n    return requestAnimationFrame.bind(global$1);\n  }\n  return function (callback) {\n    return setTimeout(function () {\n      return callback(Date.now());\n    }, 1000 / 60);\n  };\n}();\n\n// Defines minimum timeout before adding a trailing call.\nvar trailingTimeout = 2;\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\nfunction throttle(callback, delay) {\n  var leadingCall = false,\n    trailingCall = false,\n    lastCallTime = 0;\n  /**\r\n   * Invokes the original callback function and schedules new invocation if\r\n   * the \"proxy\" was called during current request.\r\n   *\r\n   * @returns {void}\r\n   */\n  function resolvePending() {\n    if (leadingCall) {\n      leadingCall = false;\n      callback();\n    }\n    if (trailingCall) {\n      proxy();\n    }\n  }\n  /**\r\n   * Callback invoked after the specified delay. It will further postpone\r\n   * invocation of the original function delegating it to the\r\n   * requestAnimationFrame.\r\n   *\r\n   * @returns {void}\r\n   */\n  function timeoutCallback() {\n    requestAnimationFrame$1(resolvePending);\n  }\n  /**\r\n   * Schedules invocation of the original function.\r\n   *\r\n   * @returns {void}\r\n   */\n  function proxy() {\n    var timeStamp = Date.now();\n    if (leadingCall) {\n      // Reject immediately following calls.\n      if (timeStamp - lastCallTime < trailingTimeout) {\n        return;\n      }\n      // Schedule new call to be in invoked when the pending one is resolved.\n      // This is important for \"transitions\" which never actually start\n      // immediately so there is a chance that we might miss one if change\n      // happens amids the pending invocation.\n      trailingCall = true;\n    } else {\n      leadingCall = true;\n      trailingCall = false;\n      setTimeout(timeoutCallback, delay);\n    }\n    lastCallTime = timeStamp;\n  }\n  return proxy;\n}\n\n// Minimum delay before invoking the update of observers.\nvar REFRESH_DELAY = 20;\n// A list of substrings of CSS properties used to find transition events that\n// might affect dimensions of observed elements.\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\n// Check if MutationObserver is available.\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\nvar ResizeObserverController = /** @class */function () {\n  /**\r\n   * Creates a new instance of ResizeObserverController.\r\n   *\r\n   * @private\r\n   */\n  function ResizeObserverController() {\n    /**\r\n     * Indicates whether DOM listeners have been added.\r\n     *\r\n     * @private {boolean}\r\n     */\n    this.connected_ = false;\n    /**\r\n     * Tells that controller has subscribed for Mutation Events.\r\n     *\r\n     * @private {boolean}\r\n     */\n    this.mutationEventsAdded_ = false;\n    /**\r\n     * Keeps reference to the instance of MutationObserver.\r\n     *\r\n     * @private {MutationObserver}\r\n     */\n    this.mutationsObserver_ = null;\n    /**\r\n     * A list of connected observers.\r\n     *\r\n     * @private {Array<ResizeObserverSPI>}\r\n     */\n    this.observers_ = [];\n    this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\n    this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\n  }\n  /**\r\n   * Adds observer to observers list.\r\n   *\r\n   * @param {ResizeObserverSPI} observer - Observer to be added.\r\n   * @returns {void}\r\n   */\n  ResizeObserverController.prototype.addObserver = function (observer) {\n    if (!~this.observers_.indexOf(observer)) {\n      this.observers_.push(observer);\n    }\n    // Add listeners if they haven't been added yet.\n    if (!this.connected_) {\n      this.connect_();\n    }\n  };\n  /**\r\n   * Removes observer from observers list.\r\n   *\r\n   * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n   * @returns {void}\r\n   */\n  ResizeObserverController.prototype.removeObserver = function (observer) {\n    var observers = this.observers_;\n    var index = observers.indexOf(observer);\n    // Remove observer if it's present in registry.\n    if (~index) {\n      observers.splice(index, 1);\n    }\n    // Remove listeners if controller has no connected observers.\n    if (!observers.length && this.connected_) {\n      this.disconnect_();\n    }\n  };\n  /**\r\n   * Invokes the update of observers. It will continue running updates insofar\r\n   * it detects changes.\r\n   *\r\n   * @returns {void}\r\n   */\n  ResizeObserverController.prototype.refresh = function () {\n    var changesDetected = this.updateObservers_();\n    // Continue running updates if changes have been detected as there might\n    // be future ones caused by CSS transitions.\n    if (changesDetected) {\n      this.refresh();\n    }\n  };\n  /**\r\n   * Updates every observer from observers list and notifies them of queued\r\n   * entries.\r\n   *\r\n   * @private\r\n   * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n   *      dimensions of it's elements.\r\n   */\n  ResizeObserverController.prototype.updateObservers_ = function () {\n    // Collect observers that have active observations.\n    var activeObservers = this.observers_.filter(function (observer) {\n      return observer.gatherActive(), observer.hasActive();\n    });\n    // Deliver notifications in a separate cycle in order to avoid any\n    // collisions between observers, e.g. when multiple instances of\n    // ResizeObserver are tracking the same element and the callback of one\n    // of them changes content dimensions of the observed target. Sometimes\n    // this may result in notifications being blocked for the rest of observers.\n    activeObservers.forEach(function (observer) {\n      return observer.broadcastActive();\n    });\n    return activeObservers.length > 0;\n  };\n  /**\r\n   * Initializes DOM listeners.\r\n   *\r\n   * @private\r\n   * @returns {void}\r\n   */\n  ResizeObserverController.prototype.connect_ = function () {\n    // Do nothing if running in a non-browser environment or if listeners\n    // have been already added.\n    if (!isBrowser || this.connected_) {\n      return;\n    }\n    // Subscription to the \"Transitionend\" event is used as a workaround for\n    // delayed transitions. This way it's possible to capture at least the\n    // final state of an element.\n    document.addEventListener('transitionend', this.onTransitionEnd_);\n    window.addEventListener('resize', this.refresh);\n    if (mutationObserverSupported) {\n      this.mutationsObserver_ = new MutationObserver(this.refresh);\n      this.mutationsObserver_.observe(document, {\n        attributes: true,\n        childList: true,\n        characterData: true,\n        subtree: true\n      });\n    } else {\n      document.addEventListener('DOMSubtreeModified', this.refresh);\n      this.mutationEventsAdded_ = true;\n    }\n    this.connected_ = true;\n  };\n  /**\r\n   * Removes DOM listeners.\r\n   *\r\n   * @private\r\n   * @returns {void}\r\n   */\n  ResizeObserverController.prototype.disconnect_ = function () {\n    // Do nothing if running in a non-browser environment or if listeners\n    // have been already removed.\n    if (!isBrowser || !this.connected_) {\n      return;\n    }\n    document.removeEventListener('transitionend', this.onTransitionEnd_);\n    window.removeEventListener('resize', this.refresh);\n    if (this.mutationsObserver_) {\n      this.mutationsObserver_.disconnect();\n    }\n    if (this.mutationEventsAdded_) {\n      document.removeEventListener('DOMSubtreeModified', this.refresh);\n    }\n    this.mutationsObserver_ = null;\n    this.mutationEventsAdded_ = false;\n    this.connected_ = false;\n  };\n  /**\r\n   * \"Transitionend\" event handler.\r\n   *\r\n   * @private\r\n   * @param {TransitionEvent} event\r\n   * @returns {void}\r\n   */\n  ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\n    var _b = _a.propertyName,\n      propertyName = _b === void 0 ? '' : _b;\n    // Detect whether transition may affect dimensions of an element.\n    var isReflowProperty = transitionKeys.some(function (key) {\n      return !!~propertyName.indexOf(key);\n    });\n    if (isReflowProperty) {\n      this.refresh();\n    }\n  };\n  /**\r\n   * Returns instance of the ResizeObserverController.\r\n   *\r\n   * @returns {ResizeObserverController}\r\n   */\n  ResizeObserverController.getInstance = function () {\n    if (!this.instance_) {\n      this.instance_ = new ResizeObserverController();\n    }\n    return this.instance_;\n  };\n  /**\r\n   * Holds reference to the controller's instance.\r\n   *\r\n   * @private {ResizeObserverController}\r\n   */\n  ResizeObserverController.instance_ = null;\n  return ResizeObserverController;\n}();\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\nvar defineConfigurable = function (target, props) {\n  for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\n    var key = _a[_i];\n    Object.defineProperty(target, key, {\n      value: props[key],\n      enumerable: false,\n      writable: false,\n      configurable: true\n    });\n  }\n  return target;\n};\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\nvar getWindowOf = function (target) {\n  // Assume that the element is an instance of Node, which means that it\n  // has the \"ownerDocument\" property from which we can retrieve a\n  // corresponding global object.\n  var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\n  // Return the local global object if it's not possible extract one from\n  // provided element.\n  return ownerGlobal || global$1;\n};\n\n// Placeholder of an empty content rectangle.\nvar emptyRect = createRectInit(0, 0, 0, 0);\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\nfunction toFloat(value) {\n  return parseFloat(value) || 0;\n}\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\nfunction getBordersSize(styles) {\n  var positions = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    positions[_i - 1] = arguments[_i];\n  }\n  return positions.reduce(function (size, position) {\n    var value = styles['border-' + position + '-width'];\n    return size + toFloat(value);\n  }, 0);\n}\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\nfunction getPaddings(styles) {\n  var positions = ['top', 'right', 'bottom', 'left'];\n  var paddings = {};\n  for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\n    var position = positions_1[_i];\n    var value = styles['padding-' + position];\n    paddings[position] = toFloat(value);\n  }\n  return paddings;\n}\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\nfunction getSVGContentRect(target) {\n  var bbox = target.getBBox();\n  return createRectInit(0, 0, bbox.width, bbox.height);\n}\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\nfunction getHTMLElementContentRect(target) {\n  // Client width & height properties can't be\n  // used exclusively as they provide rounded values.\n  var clientWidth = target.clientWidth,\n    clientHeight = target.clientHeight;\n  // By this condition we can catch all non-replaced inline, hidden and\n  // detached elements. Though elements with width & height properties less\n  // than 0.5 will be discarded as well.\n  //\n  // Without it we would need to implement separate methods for each of\n  // those cases and it's not possible to perform a precise and performance\n  // effective test for hidden elements. E.g. even jQuery's ':visible' filter\n  // gives wrong results for elements with width & height less than 0.5.\n  if (!clientWidth && !clientHeight) {\n    return emptyRect;\n  }\n  var styles = getWindowOf(target).getComputedStyle(target);\n  var paddings = getPaddings(styles);\n  var horizPad = paddings.left + paddings.right;\n  var vertPad = paddings.top + paddings.bottom;\n  // Computed styles of width & height are being used because they are the\n  // only dimensions available to JS that contain non-rounded values. It could\n  // be possible to utilize the getBoundingClientRect if only it's data wasn't\n  // affected by CSS transformations let alone paddings, borders and scroll bars.\n  var width = toFloat(styles.width),\n    height = toFloat(styles.height);\n  // Width & height include paddings and borders when the 'border-box' box\n  // model is applied (except for IE).\n  if (styles.boxSizing === 'border-box') {\n    // Following conditions are required to handle Internet Explorer which\n    // doesn't include paddings and borders to computed CSS dimensions.\n    //\n    // We can say that if CSS dimensions + paddings are equal to the \"client\"\n    // properties then it's either IE, and thus we don't need to subtract\n    // anything, or an element merely doesn't have paddings/borders styles.\n    if (Math.round(width + horizPad) !== clientWidth) {\n      width -= getBordersSize(styles, 'left', 'right') + horizPad;\n    }\n    if (Math.round(height + vertPad) !== clientHeight) {\n      height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\n    }\n  }\n  // Following steps can't be applied to the document's root element as its\n  // client[Width/Height] properties represent viewport area of the window.\n  // Besides, it's as well not necessary as the <html> itself neither has\n  // rendered scroll bars nor it can be clipped.\n  if (!isDocumentElement(target)) {\n    // In some browsers (only in Firefox, actually) CSS width & height\n    // include scroll bars size which can be removed at this step as scroll\n    // bars are the only difference between rounded dimensions + paddings\n    // and \"client\" properties, though that is not always true in Chrome.\n    var vertScrollbar = Math.round(width + horizPad) - clientWidth;\n    var horizScrollbar = Math.round(height + vertPad) - clientHeight;\n    // Chrome has a rather weird rounding of \"client\" properties.\n    // E.g. for an element with content width of 314.2px it sometimes gives\n    // the client width of 315px and for the width of 314.7px it may give\n    // 314px. And it doesn't happen all the time. So just ignore this delta\n    // as a non-relevant.\n    if (Math.abs(vertScrollbar) !== 1) {\n      width -= vertScrollbar;\n    }\n    if (Math.abs(horizScrollbar) !== 1) {\n      height -= horizScrollbar;\n    }\n  }\n  return createRectInit(paddings.left, paddings.top, width, height);\n}\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\nvar isSVGGraphicsElement = function () {\n  // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\n  // interface.\n  if (typeof SVGGraphicsElement !== 'undefined') {\n    return function (target) {\n      return target instanceof getWindowOf(target).SVGGraphicsElement;\n    };\n  }\n  // If it's so, then check that element is at least an instance of the\n  // SVGElement and that it has the \"getBBox\" method.\n  // eslint-disable-next-line no-extra-parens\n  return function (target) {\n    return target instanceof getWindowOf(target).SVGElement && typeof target.getBBox === 'function';\n  };\n}();\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\nfunction isDocumentElement(target) {\n  return target === getWindowOf(target).document.documentElement;\n}\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\nfunction getContentRect(target) {\n  if (!isBrowser) {\n    return emptyRect;\n  }\n  if (isSVGGraphicsElement(target)) {\n    return getSVGContentRect(target);\n  }\n  return getHTMLElementContentRect(target);\n}\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\nfunction createReadOnlyRect(_a) {\n  var x = _a.x,\n    y = _a.y,\n    width = _a.width,\n    height = _a.height;\n  // If DOMRectReadOnly is available use it as a prototype for the rectangle.\n  var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\n  var rect = Object.create(Constr.prototype);\n  // Rectangle's properties are not writable and non-enumerable.\n  defineConfigurable(rect, {\n    x: x,\n    y: y,\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: height + y,\n    left: x\n  });\n  return rect;\n}\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\nfunction createRectInit(x, y, width, height) {\n  return {\n    x: x,\n    y: y,\n    width: width,\n    height: height\n  };\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\nvar ResizeObservation = /** @class */function () {\n  /**\r\n   * Creates an instance of ResizeObservation.\r\n   *\r\n   * @param {Element} target - Element to be observed.\r\n   */\n  function ResizeObservation(target) {\n    /**\r\n     * Broadcasted width of content rectangle.\r\n     *\r\n     * @type {number}\r\n     */\n    this.broadcastWidth = 0;\n    /**\r\n     * Broadcasted height of content rectangle.\r\n     *\r\n     * @type {number}\r\n     */\n    this.broadcastHeight = 0;\n    /**\r\n     * Reference to the last observed content rectangle.\r\n     *\r\n     * @private {DOMRectInit}\r\n     */\n    this.contentRect_ = createRectInit(0, 0, 0, 0);\n    this.target = target;\n  }\n  /**\r\n   * Updates content rectangle and tells whether it's width or height properties\r\n   * have changed since the last broadcast.\r\n   *\r\n   * @returns {boolean}\r\n   */\n  ResizeObservation.prototype.isActive = function () {\n    var rect = getContentRect(this.target);\n    this.contentRect_ = rect;\n    return rect.width !== this.broadcastWidth || rect.height !== this.broadcastHeight;\n  };\n  /**\r\n   * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n   * from the corresponding properties of the last observed content rectangle.\r\n   *\r\n   * @returns {DOMRectInit} Last observed content rectangle.\r\n   */\n  ResizeObservation.prototype.broadcastRect = function () {\n    var rect = this.contentRect_;\n    this.broadcastWidth = rect.width;\n    this.broadcastHeight = rect.height;\n    return rect;\n  };\n  return ResizeObservation;\n}();\nvar ResizeObserverEntry = /** @class */function () {\n  /**\r\n   * Creates an instance of ResizeObserverEntry.\r\n   *\r\n   * @param {Element} target - Element that is being observed.\r\n   * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n   */\n  function ResizeObserverEntry(target, rectInit) {\n    var contentRect = createReadOnlyRect(rectInit);\n    // According to the specification following properties are not writable\n    // and are also not enumerable in the native implementation.\n    //\n    // Property accessors are not being used as they'd require to define a\n    // private WeakMap storage which may cause memory leaks in browsers that\n    // don't support this type of collections.\n    defineConfigurable(this, {\n      target: target,\n      contentRect: contentRect\n    });\n  }\n  return ResizeObserverEntry;\n}();\nvar ResizeObserverSPI = /** @class */function () {\n  /**\r\n   * Creates a new instance of ResizeObserver.\r\n   *\r\n   * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n   *      when one of the observed elements changes it's content dimensions.\r\n   * @param {ResizeObserverController} controller - Controller instance which\r\n   *      is responsible for the updates of observer.\r\n   * @param {ResizeObserver} callbackCtx - Reference to the public\r\n   *      ResizeObserver instance which will be passed to callback function.\r\n   */\n  function ResizeObserverSPI(callback, controller, callbackCtx) {\n    /**\r\n     * Collection of resize observations that have detected changes in dimensions\r\n     * of elements.\r\n     *\r\n     * @private {Array<ResizeObservation>}\r\n     */\n    this.activeObservations_ = [];\n    /**\r\n     * Registry of the ResizeObservation instances.\r\n     *\r\n     * @private {Map<Element, ResizeObservation>}\r\n     */\n    this.observations_ = new MapShim();\n    if (typeof callback !== 'function') {\n      throw new TypeError('The callback provided as parameter 1 is not a function.');\n    }\n    this.callback_ = callback;\n    this.controller_ = controller;\n    this.callbackCtx_ = callbackCtx;\n  }\n  /**\r\n   * Starts observing provided element.\r\n   *\r\n   * @param {Element} target - Element to be observed.\r\n   * @returns {void}\r\n   */\n  ResizeObserverSPI.prototype.observe = function (target) {\n    if (!arguments.length) {\n      throw new TypeError('1 argument required, but only 0 present.');\n    }\n    // Do nothing if current environment doesn't have the Element interface.\n    if (typeof Element === 'undefined' || !(Element instanceof Object)) {\n      return;\n    }\n    if (!(target instanceof getWindowOf(target).Element)) {\n      throw new TypeError('parameter 1 is not of type \"Element\".');\n    }\n    var observations = this.observations_;\n    // Do nothing if element is already being observed.\n    if (observations.has(target)) {\n      return;\n    }\n    observations.set(target, new ResizeObservation(target));\n    this.controller_.addObserver(this);\n    // Force the update of observations.\n    this.controller_.refresh();\n  };\n  /**\r\n   * Stops observing provided element.\r\n   *\r\n   * @param {Element} target - Element to stop observing.\r\n   * @returns {void}\r\n   */\n  ResizeObserverSPI.prototype.unobserve = function (target) {\n    if (!arguments.length) {\n      throw new TypeError('1 argument required, but only 0 present.');\n    }\n    // Do nothing if current environment doesn't have the Element interface.\n    if (typeof Element === 'undefined' || !(Element instanceof Object)) {\n      return;\n    }\n    if (!(target instanceof getWindowOf(target).Element)) {\n      throw new TypeError('parameter 1 is not of type \"Element\".');\n    }\n    var observations = this.observations_;\n    // Do nothing if element is not being observed.\n    if (!observations.has(target)) {\n      return;\n    }\n    observations.delete(target);\n    if (!observations.size) {\n      this.controller_.removeObserver(this);\n    }\n  };\n  /**\r\n   * Stops observing all elements.\r\n   *\r\n   * @returns {void}\r\n   */\n  ResizeObserverSPI.prototype.disconnect = function () {\n    this.clearActive();\n    this.observations_.clear();\n    this.controller_.removeObserver(this);\n  };\n  /**\r\n   * Collects observation instances the associated element of which has changed\r\n   * it's content rectangle.\r\n   *\r\n   * @returns {void}\r\n   */\n  ResizeObserverSPI.prototype.gatherActive = function () {\n    var _this = this;\n    this.clearActive();\n    this.observations_.forEach(function (observation) {\n      if (observation.isActive()) {\n        _this.activeObservations_.push(observation);\n      }\n    });\n  };\n  /**\r\n   * Invokes initial callback function with a list of ResizeObserverEntry\r\n   * instances collected from active resize observations.\r\n   *\r\n   * @returns {void}\r\n   */\n  ResizeObserverSPI.prototype.broadcastActive = function () {\n    // Do nothing if observer doesn't have active observations.\n    if (!this.hasActive()) {\n      return;\n    }\n    var ctx = this.callbackCtx_;\n    // Create ResizeObserverEntry instance for every active observation.\n    var entries = this.activeObservations_.map(function (observation) {\n      return new ResizeObserverEntry(observation.target, observation.broadcastRect());\n    });\n    this.callback_.call(ctx, entries, ctx);\n    this.clearActive();\n  };\n  /**\r\n   * Clears the collection of active observations.\r\n   *\r\n   * @returns {void}\r\n   */\n  ResizeObserverSPI.prototype.clearActive = function () {\n    this.activeObservations_.splice(0);\n  };\n  /**\r\n   * Tells whether observer has active observations.\r\n   *\r\n   * @returns {boolean}\r\n   */\n  ResizeObserverSPI.prototype.hasActive = function () {\n    return this.activeObservations_.length > 0;\n  };\n  return ResizeObserverSPI;\n}();\n\n// Registry of internal observers. If WeakMap is not available use current shim\n// for the Map collection as it has all required methods and because WeakMap\n// can't be fully polyfilled anyway.\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\nvar ResizeObserver = /** @class */function () {\n  /**\r\n   * Creates a new instance of ResizeObserver.\r\n   *\r\n   * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n   *      dimensions of the observed elements change.\r\n   */\n  function ResizeObserver(callback) {\n    if (!(this instanceof ResizeObserver)) {\n      throw new TypeError('Cannot call a class as a function.');\n    }\n    if (!arguments.length) {\n      throw new TypeError('1 argument required, but only 0 present.');\n    }\n    var controller = ResizeObserverController.getInstance();\n    var observer = new ResizeObserverSPI(callback, controller, this);\n    observers.set(this, observer);\n  }\n  return ResizeObserver;\n}();\n// Expose public methods of ResizeObserver.\n['observe', 'unobserve', 'disconnect'].forEach(function (method) {\n  ResizeObserver.prototype[method] = function () {\n    var _a;\n    return (_a = observers.get(this))[method].apply(_a, arguments);\n  };\n});\nvar index = function () {\n  // Export existing implementation if available.\n  if (typeof global$1.ResizeObserver !== 'undefined') {\n    return global$1.ResizeObserver;\n  }\n  return ResizeObserver;\n}();\nexport default index;", "map": {"version": 3, "names": ["MapShim", "Map", "getIndex", "arr", "key", "result", "some", "entry", "index", "class_1", "__entries__", "Object", "defineProperty", "prototype", "get", "length", "enumerable", "configurable", "set", "value", "push", "delete", "entries", "splice", "has", "clear", "for<PERSON>ach", "callback", "ctx", "_i", "_a", "call", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "global$1", "global", "Math", "self", "Function", "requestAnimationFrame$1", "requestAnimationFrame", "bind", "setTimeout", "Date", "now", "trailingTimeout", "throttle", "delay", "leadingCall", "trailingCall", "lastCallTime", "resolvePending", "proxy", "timeout<PERSON><PERSON><PERSON>", "timeStamp", "REFRESH_DELAY", "<PERSON><PERSON><PERSON><PERSON>", "mutationObserverSupported", "MutationObserver", "ResizeObserverController", "connected_", "mutationEventsAdded_", "mutationsObserver_", "observers_", "onTransitionEnd_", "refresh", "addObserver", "observer", "indexOf", "connect_", "removeObserver", "observers", "disconnect_", "changesDetected", "updateObservers_", "activeObservers", "filter", "gatherActive", "hasActive", "broadcastActive", "addEventListener", "observe", "attributes", "childList", "characterData", "subtree", "removeEventListener", "disconnect", "_b", "propertyName", "isReflowProperty", "getInstance", "instance_", "defineConfigurable", "target", "props", "keys", "writable", "getWindowOf", "ownerGlobal", "ownerDocument", "defaultView", "emptyRect", "createRectInit", "toFloat", "parseFloat", "getBordersSize", "styles", "positions", "arguments", "reduce", "size", "position", "getPaddings", "paddings", "positions_1", "getSVGContentRect", "bbox", "getBBox", "width", "height", "getHTMLElementContentRect", "clientWidth", "clientHeight", "getComputedStyle", "horizPad", "left", "right", "vertPad", "top", "bottom", "boxSizing", "round", "isDocumentElement", "vertScrollbar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "abs", "isSVGGraphicsElement", "SVGGraphicsElement", "SVGElement", "documentElement", "getContentRect", "createReadOnlyRect", "x", "y", "Constr", "DOMRectReadOnly", "rect", "create", "ResizeObservation", "broadcastWidth", "broadcastHeight", "contentRect_", "isActive", "broadcastRect", "ResizeObserverEntry", "rectInit", "contentRect", "ResizeObserverSPI", "controller", "callbackCtx", "activeObservations_", "observations_", "TypeError", "callback_", "controller_", "callbackCtx_", "Element", "observations", "unobserve", "clearActive", "_this", "observation", "map", "WeakMap", "ResizeObserver", "method", "apply"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js"], "sourcesContent": ["/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,OAAO,GAAI,YAAY;EACvB,IAAI,OAAOC,GAAG,KAAK,WAAW,EAAE;IAC5B,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,QAAQA,CAACC,GAAG,EAAEC,GAAG,EAAE;IACxB,IAAIC,MAAM,GAAG,CAAC,CAAC;IACfF,GAAG,CAACG,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MAC7B,IAAID,KAAK,CAAC,CAAC,CAAC,KAAKH,GAAG,EAAE;QAClBC,MAAM,GAAGG,KAAK;QACd,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB,CAAC,CAAC;IACF,OAAOH,MAAM;EACjB;EACA,OAAO,aAAe,YAAY;IAC9B,SAASI,OAAOA,CAAA,EAAG;MACf,IAAI,CAACC,WAAW,GAAG,EAAE;IACzB;IACAC,MAAM,CAACC,cAAc,CAACH,OAAO,CAACI,SAAS,EAAE,MAAM,EAAE;MAC7C;AACZ;AACA;MACYC,GAAG,EAAE,SAAAA,CAAA,EAAY;QACb,OAAO,IAAI,CAACJ,WAAW,CAACK,MAAM;MAClC,CAAC;MACDC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE;IAClB,CAAC,CAAC;IACF;AACR;AACA;AACA;IACQR,OAAO,CAACI,SAAS,CAACC,GAAG,GAAG,UAAUV,GAAG,EAAE;MACnC,IAAII,KAAK,GAAGN,QAAQ,CAAC,IAAI,CAACQ,WAAW,EAAEN,GAAG,CAAC;MAC3C,IAAIG,KAAK,GAAG,IAAI,CAACG,WAAW,CAACF,KAAK,CAAC;MACnC,OAAOD,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD;AACR;AACA;AACA;AACA;IACQE,OAAO,CAACI,SAAS,CAACK,GAAG,GAAG,UAAUd,GAAG,EAAEe,KAAK,EAAE;MAC1C,IAAIX,KAAK,GAAGN,QAAQ,CAAC,IAAI,CAACQ,WAAW,EAAEN,GAAG,CAAC;MAC3C,IAAI,CAACI,KAAK,EAAE;QACR,IAAI,CAACE,WAAW,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGW,KAAK;MACtC,CAAC,MACI;QACD,IAAI,CAACT,WAAW,CAACU,IAAI,CAAC,CAAChB,GAAG,EAAEe,KAAK,CAAC,CAAC;MACvC;IACJ,CAAC;IACD;AACR;AACA;AACA;IACQV,OAAO,CAACI,SAAS,CAACQ,MAAM,GAAG,UAAUjB,GAAG,EAAE;MACtC,IAAIkB,OAAO,GAAG,IAAI,CAACZ,WAAW;MAC9B,IAAIF,KAAK,GAAGN,QAAQ,CAACoB,OAAO,EAAElB,GAAG,CAAC;MAClC,IAAI,CAACI,KAAK,EAAE;QACRc,OAAO,CAACC,MAAM,CAACf,KAAK,EAAE,CAAC,CAAC;MAC5B;IACJ,CAAC;IACD;AACR;AACA;AACA;IACQC,OAAO,CAACI,SAAS,CAACW,GAAG,GAAG,UAAUpB,GAAG,EAAE;MACnC,OAAO,CAAC,CAAC,CAACF,QAAQ,CAAC,IAAI,CAACQ,WAAW,EAAEN,GAAG,CAAC;IAC7C,CAAC;IACD;AACR;AACA;IACQK,OAAO,CAACI,SAAS,CAACY,KAAK,GAAG,YAAY;MAClC,IAAI,CAACf,WAAW,CAACa,MAAM,CAAC,CAAC,CAAC;IAC9B,CAAC;IACD;AACR;AACA;AACA;AACA;IACQd,OAAO,CAACI,SAAS,CAACa,OAAO,GAAG,UAAUC,QAAQ,EAAEC,GAAG,EAAE;MACjD,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;QAAEA,GAAG,GAAG,IAAI;MAAE;MAClC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,IAAI,CAACpB,WAAW,EAAEmB,EAAE,GAAGC,EAAE,CAACf,MAAM,EAAEc,EAAE,EAAE,EAAE;QAC1D,IAAItB,KAAK,GAAGuB,EAAE,CAACD,EAAE,CAAC;QAClBF,QAAQ,CAACI,IAAI,CAACH,GAAG,EAAErB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1C;IACJ,CAAC;IACD,OAAOE,OAAO;EAClB,CAAC,CAAC,CAAC;AACP,CAAC,CAAE,CAAC;;AAEJ;AACA;AACA;AACA,IAAIuB,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAID,MAAM,CAACC,QAAQ,KAAKA,QAAQ;;AAEhH;AACA,IAAIC,QAAQ,GAAI,YAAY;EACxB,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,IAAI,KAAKA,IAAI,EAAE;IACvD,OAAOD,MAAM;EACjB;EACA,IAAI,OAAOE,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACD,IAAI,KAAKA,IAAI,EAAE;IACnD,OAAOC,IAAI;EACf;EACA,IAAI,OAAOL,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACI,IAAI,KAAKA,IAAI,EAAE;IACvD,OAAOJ,MAAM;EACjB;EACA;EACA,OAAOM,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;AACpC,CAAC,CAAE,CAAC;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,uBAAuB,GAAI,YAAY;EACvC,IAAI,OAAOC,qBAAqB,KAAK,UAAU,EAAE;IAC7C;IACA;IACA;IACA,OAAOA,qBAAqB,CAACC,IAAI,CAACP,QAAQ,CAAC;EAC/C;EACA,OAAO,UAAUR,QAAQ,EAAE;IAAE,OAAOgB,UAAU,CAAC,YAAY;MAAE,OAAOhB,QAAQ,CAACiB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC;EAAE,CAAC;AAC9G,CAAC,CAAE,CAAC;;AAEJ;AACA,IAAIC,eAAe,GAAG,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAEpB,QAAQ,EAAEqB,KAAK,EAAE;EAChC,IAAIC,WAAW,GAAG,KAAK;IAAEC,YAAY,GAAG,KAAK;IAAEC,YAAY,GAAG,CAAC;EAC/D;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,cAAcA,CAAA,EAAG;IACtB,IAAIH,WAAW,EAAE;MACbA,WAAW,GAAG,KAAK;MACnBtB,QAAQ,CAAC,CAAC;IACd;IACA,IAAIuB,YAAY,EAAE;MACdG,KAAK,CAAC,CAAC;IACX;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,eAAeA,CAAA,EAAG;IACvBd,uBAAuB,CAACY,cAAc,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACI,SAASC,KAAKA,CAAA,EAAG;IACb,IAAIE,SAAS,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1B,IAAII,WAAW,EAAE;MACb;MACA,IAAIM,SAAS,GAAGJ,YAAY,GAAGL,eAAe,EAAE;QAC5C;MACJ;MACA;MACA;MACA;MACA;MACAI,YAAY,GAAG,IAAI;IACvB,CAAC,MACI;MACDD,WAAW,GAAG,IAAI;MAClBC,YAAY,GAAG,KAAK;MACpBP,UAAU,CAACW,eAAe,EAAEN,KAAK,CAAC;IACtC;IACAG,YAAY,GAAGI,SAAS;EAC5B;EACA,OAAOF,KAAK;AAChB;;AAEA;AACA,IAAIG,aAAa,GAAG,EAAE;AACtB;AACA;AACA,IAAIC,cAAc,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC5F;AACA,IAAIC,yBAAyB,GAAG,OAAOC,gBAAgB,KAAK,WAAW;AACvE;AACA;AACA;AACA,IAAIC,wBAAwB,GAAG,aAAe,YAAY;EACtD;AACJ;AACA;AACA;AACA;EACI,SAASA,wBAAwBA,CAAA,EAAG;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACvB,IAAI,CAAC,IAAI,CAAC;IACxD,IAAI,CAACwB,OAAO,GAAGnB,QAAQ,CAAC,IAAI,CAACmB,OAAO,CAACxB,IAAI,CAAC,IAAI,CAAC,EAAEc,aAAa,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,wBAAwB,CAAC/C,SAAS,CAACsD,WAAW,GAAG,UAAUC,QAAQ,EAAE;IACjE,IAAI,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACD,QAAQ,CAAC,EAAE;MACrC,IAAI,CAACJ,UAAU,CAAC5C,IAAI,CAACgD,QAAQ,CAAC;IAClC;IACA;IACA,IAAI,CAAC,IAAI,CAACP,UAAU,EAAE;MAClB,IAAI,CAACS,QAAQ,CAAC,CAAC;IACnB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIV,wBAAwB,CAAC/C,SAAS,CAAC0D,cAAc,GAAG,UAAUH,QAAQ,EAAE;IACpE,IAAII,SAAS,GAAG,IAAI,CAACR,UAAU;IAC/B,IAAIxD,KAAK,GAAGgE,SAAS,CAACH,OAAO,CAACD,QAAQ,CAAC;IACvC;IACA,IAAI,CAAC5D,KAAK,EAAE;MACRgE,SAAS,CAACjD,MAAM,CAACf,KAAK,EAAE,CAAC,CAAC;IAC9B;IACA;IACA,IAAI,CAACgE,SAAS,CAACzD,MAAM,IAAI,IAAI,CAAC8C,UAAU,EAAE;MACtC,IAAI,CAACY,WAAW,CAAC,CAAC;IACtB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIb,wBAAwB,CAAC/C,SAAS,CAACqD,OAAO,GAAG,YAAY;IACrD,IAAIQ,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C;IACA;IACA,IAAID,eAAe,EAAE;MACjB,IAAI,CAACR,OAAO,CAAC,CAAC;IAClB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,wBAAwB,CAAC/C,SAAS,CAAC8D,gBAAgB,GAAG,YAAY;IAC9D;IACA,IAAIC,eAAe,GAAG,IAAI,CAACZ,UAAU,CAACa,MAAM,CAAC,UAAUT,QAAQ,EAAE;MAC7D,OAAOA,QAAQ,CAACU,YAAY,CAAC,CAAC,EAAEV,QAAQ,CAACW,SAAS,CAAC,CAAC;IACxD,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACAH,eAAe,CAAClD,OAAO,CAAC,UAAU0C,QAAQ,EAAE;MAAE,OAAOA,QAAQ,CAACY,eAAe,CAAC,CAAC;IAAE,CAAC,CAAC;IACnF,OAAOJ,eAAe,CAAC7D,MAAM,GAAG,CAAC;EACrC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI6C,wBAAwB,CAAC/C,SAAS,CAACyD,QAAQ,GAAG,YAAY;IACtD;IACA;IACA,IAAI,CAACtC,SAAS,IAAI,IAAI,CAAC6B,UAAU,EAAE;MAC/B;IACJ;IACA;IACA;IACA;IACA3B,QAAQ,CAAC+C,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAChB,gBAAgB,CAAC;IACjEhC,MAAM,CAACgD,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACf,OAAO,CAAC;IAC/C,IAAIR,yBAAyB,EAAE;MAC3B,IAAI,CAACK,kBAAkB,GAAG,IAAIJ,gBAAgB,CAAC,IAAI,CAACO,OAAO,CAAC;MAC5D,IAAI,CAACH,kBAAkB,CAACmB,OAAO,CAAChD,QAAQ,EAAE;QACtCiD,UAAU,EAAE,IAAI;QAChBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,IAAI;QACnBC,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,MACI;MACDpD,QAAQ,CAAC+C,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,CAACf,OAAO,CAAC;MAC7D,IAAI,CAACJ,oBAAoB,GAAG,IAAI;IACpC;IACA,IAAI,CAACD,UAAU,GAAG,IAAI;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACID,wBAAwB,CAAC/C,SAAS,CAAC4D,WAAW,GAAG,YAAY;IACzD;IACA;IACA,IAAI,CAACzC,SAAS,IAAI,CAAC,IAAI,CAAC6B,UAAU,EAAE;MAChC;IACJ;IACA3B,QAAQ,CAACqD,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACtB,gBAAgB,CAAC;IACpEhC,MAAM,CAACsD,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACrB,OAAO,CAAC;IAClD,IAAI,IAAI,CAACH,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAACyB,UAAU,CAAC,CAAC;IACxC;IACA,IAAI,IAAI,CAAC1B,oBAAoB,EAAE;MAC3B5B,QAAQ,CAACqD,mBAAmB,CAAC,oBAAoB,EAAE,IAAI,CAACrB,OAAO,CAAC;IACpE;IACA,IAAI,CAACH,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACD,UAAU,GAAG,KAAK;EAC3B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,wBAAwB,CAAC/C,SAAS,CAACoD,gBAAgB,GAAG,UAAUnC,EAAE,EAAE;IAChE,IAAI2D,EAAE,GAAG3D,EAAE,CAAC4D,YAAY;MAAEA,YAAY,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAChE;IACA,IAAIE,gBAAgB,GAAGlC,cAAc,CAACnD,IAAI,CAAC,UAAUF,GAAG,EAAE;MACtD,OAAO,CAAC,CAAC,CAACsF,YAAY,CAACrB,OAAO,CAACjE,GAAG,CAAC;IACvC,CAAC,CAAC;IACF,IAAIuF,gBAAgB,EAAE;MAClB,IAAI,CAACzB,OAAO,CAAC,CAAC;IAClB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIN,wBAAwB,CAACgC,WAAW,GAAG,YAAY;IAC/C,IAAI,CAAC,IAAI,CAACC,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAIjC,wBAAwB,CAAC,CAAC;IACnD;IACA,OAAO,IAAI,CAACiC,SAAS;EACzB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIjC,wBAAwB,CAACiC,SAAS,GAAG,IAAI;EACzC,OAAOjC,wBAAwB;AACnC,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIkC,kBAAkB,GAAI,SAAAA,CAAUC,MAAM,EAAEC,KAAK,EAAE;EAC/C,KAAK,IAAInE,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGnB,MAAM,CAACsF,IAAI,CAACD,KAAK,CAAC,EAAEnE,EAAE,GAAGC,EAAE,CAACf,MAAM,EAAEc,EAAE,EAAE,EAAE;IAC5D,IAAIzB,GAAG,GAAG0B,EAAE,CAACD,EAAE,CAAC;IAChBlB,MAAM,CAACC,cAAc,CAACmF,MAAM,EAAE3F,GAAG,EAAE;MAC/Be,KAAK,EAAE6E,KAAK,CAAC5F,GAAG,CAAC;MACjBY,UAAU,EAAE,KAAK;MACjBkF,QAAQ,EAAE,KAAK;MACfjF,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA,OAAO8E,MAAM;AACjB,CAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,WAAW,GAAI,SAAAA,CAAUJ,MAAM,EAAE;EACjC;EACA;EACA;EACA,IAAIK,WAAW,GAAGL,MAAM,IAAIA,MAAM,CAACM,aAAa,IAAIN,MAAM,CAACM,aAAa,CAACC,WAAW;EACpF;EACA;EACA,OAAOF,WAAW,IAAIjE,QAAQ;AAClC,CAAE;;AAEF;AACA,IAAIoE,SAAS,GAAGC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACtF,KAAK,EAAE;EACpB,OAAOuF,UAAU,CAACvF,KAAK,CAAC,IAAI,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwF,cAAcA,CAACC,MAAM,EAAE;EAC5B,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIhF,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGiF,SAAS,CAAC/F,MAAM,EAAEc,EAAE,EAAE,EAAE;IAC1CgF,SAAS,CAAChF,EAAE,GAAG,CAAC,CAAC,GAAGiF,SAAS,CAACjF,EAAE,CAAC;EACrC;EACA,OAAOgF,SAAS,CAACE,MAAM,CAAC,UAAUC,IAAI,EAAEC,QAAQ,EAAE;IAC9C,IAAI9F,KAAK,GAAGyF,MAAM,CAAC,SAAS,GAAGK,QAAQ,GAAG,QAAQ,CAAC;IACnD,OAAOD,IAAI,GAAGP,OAAO,CAACtF,KAAK,CAAC;EAChC,CAAC,EAAE,CAAC,CAAC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+F,WAAWA,CAACN,MAAM,EAAE;EACzB,IAAIC,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;EAClD,IAAIM,QAAQ,GAAG,CAAC,CAAC;EACjB,KAAK,IAAItF,EAAE,GAAG,CAAC,EAAEuF,WAAW,GAAGP,SAAS,EAAEhF,EAAE,GAAGuF,WAAW,CAACrG,MAAM,EAAEc,EAAE,EAAE,EAAE;IACrE,IAAIoF,QAAQ,GAAGG,WAAW,CAACvF,EAAE,CAAC;IAC9B,IAAIV,KAAK,GAAGyF,MAAM,CAAC,UAAU,GAAGK,QAAQ,CAAC;IACzCE,QAAQ,CAACF,QAAQ,CAAC,GAAGR,OAAO,CAACtF,KAAK,CAAC;EACvC;EACA,OAAOgG,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACtB,MAAM,EAAE;EAC/B,IAAIuB,IAAI,GAAGvB,MAAM,CAACwB,OAAO,CAAC,CAAC;EAC3B,OAAOf,cAAc,CAAC,CAAC,EAAE,CAAC,EAAEc,IAAI,CAACE,KAAK,EAAEF,IAAI,CAACG,MAAM,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,yBAAyBA,CAAC3B,MAAM,EAAE;EACvC;EACA;EACA,IAAI4B,WAAW,GAAG5B,MAAM,CAAC4B,WAAW;IAAEC,YAAY,GAAG7B,MAAM,CAAC6B,YAAY;EACxE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACD,WAAW,IAAI,CAACC,YAAY,EAAE;IAC/B,OAAOrB,SAAS;EACpB;EACA,IAAIK,MAAM,GAAGT,WAAW,CAACJ,MAAM,CAAC,CAAC8B,gBAAgB,CAAC9B,MAAM,CAAC;EACzD,IAAIoB,QAAQ,GAAGD,WAAW,CAACN,MAAM,CAAC;EAClC,IAAIkB,QAAQ,GAAGX,QAAQ,CAACY,IAAI,GAAGZ,QAAQ,CAACa,KAAK;EAC7C,IAAIC,OAAO,GAAGd,QAAQ,CAACe,GAAG,GAAGf,QAAQ,CAACgB,MAAM;EAC5C;EACA;EACA;EACA;EACA,IAAIX,KAAK,GAAGf,OAAO,CAACG,MAAM,CAACY,KAAK,CAAC;IAAEC,MAAM,GAAGhB,OAAO,CAACG,MAAM,CAACa,MAAM,CAAC;EAClE;EACA;EACA,IAAIb,MAAM,CAACwB,SAAS,KAAK,YAAY,EAAE;IACnC;IACA;IACA;IACA;IACA;IACA;IACA,IAAI/F,IAAI,CAACgG,KAAK,CAACb,KAAK,GAAGM,QAAQ,CAAC,KAAKH,WAAW,EAAE;MAC9CH,KAAK,IAAIb,cAAc,CAACC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,GAAGkB,QAAQ;IAC/D;IACA,IAAIzF,IAAI,CAACgG,KAAK,CAACZ,MAAM,GAAGQ,OAAO,CAAC,KAAKL,YAAY,EAAE;MAC/CH,MAAM,IAAId,cAAc,CAACC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,GAAGqB,OAAO;IAC/D;EACJ;EACA;EACA;EACA;EACA;EACA,IAAI,CAACK,iBAAiB,CAACvC,MAAM,CAAC,EAAE;IAC5B;IACA;IACA;IACA;IACA,IAAIwC,aAAa,GAAGlG,IAAI,CAACgG,KAAK,CAACb,KAAK,GAAGM,QAAQ,CAAC,GAAGH,WAAW;IAC9D,IAAIa,cAAc,GAAGnG,IAAI,CAACgG,KAAK,CAACZ,MAAM,GAAGQ,OAAO,CAAC,GAAGL,YAAY;IAChE;IACA;IACA;IACA;IACA;IACA,IAAIvF,IAAI,CAACoG,GAAG,CAACF,aAAa,CAAC,KAAK,CAAC,EAAE;MAC/Bf,KAAK,IAAIe,aAAa;IAC1B;IACA,IAAIlG,IAAI,CAACoG,GAAG,CAACD,cAAc,CAAC,KAAK,CAAC,EAAE;MAChCf,MAAM,IAAIe,cAAc;IAC5B;EACJ;EACA,OAAOhC,cAAc,CAACW,QAAQ,CAACY,IAAI,EAAEZ,QAAQ,CAACe,GAAG,EAAEV,KAAK,EAAEC,MAAM,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIiB,oBAAoB,GAAI,YAAY;EACpC;EACA;EACA,IAAI,OAAOC,kBAAkB,KAAK,WAAW,EAAE;IAC3C,OAAO,UAAU5C,MAAM,EAAE;MAAE,OAAOA,MAAM,YAAYI,WAAW,CAACJ,MAAM,CAAC,CAAC4C,kBAAkB;IAAE,CAAC;EACjG;EACA;EACA;EACA;EACA,OAAO,UAAU5C,MAAM,EAAE;IAAE,OAAQA,MAAM,YAAYI,WAAW,CAACJ,MAAM,CAAC,CAAC6C,UAAU,IAC/E,OAAO7C,MAAM,CAACwB,OAAO,KAAK,UAAU;EAAG,CAAC;AAChD,CAAC,CAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,iBAAiBA,CAACvC,MAAM,EAAE;EAC/B,OAAOA,MAAM,KAAKI,WAAW,CAACJ,MAAM,CAAC,CAAC7D,QAAQ,CAAC2G,eAAe;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAC/C,MAAM,EAAE;EAC5B,IAAI,CAAC/D,SAAS,EAAE;IACZ,OAAOuE,SAAS;EACpB;EACA,IAAImC,oBAAoB,CAAC3C,MAAM,CAAC,EAAE;IAC9B,OAAOsB,iBAAiB,CAACtB,MAAM,CAAC;EACpC;EACA,OAAO2B,yBAAyB,CAAC3B,MAAM,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgD,kBAAkBA,CAACjH,EAAE,EAAE;EAC5B,IAAIkH,CAAC,GAAGlH,EAAE,CAACkH,CAAC;IAAEC,CAAC,GAAGnH,EAAE,CAACmH,CAAC;IAAEzB,KAAK,GAAG1F,EAAE,CAAC0F,KAAK;IAAEC,MAAM,GAAG3F,EAAE,CAAC2F,MAAM;EAC5D;EACA,IAAIyB,MAAM,GAAG,OAAOC,eAAe,KAAK,WAAW,GAAGA,eAAe,GAAGxI,MAAM;EAC9E,IAAIyI,IAAI,GAAGzI,MAAM,CAAC0I,MAAM,CAACH,MAAM,CAACrI,SAAS,CAAC;EAC1C;EACAiF,kBAAkB,CAACsD,IAAI,EAAE;IACrBJ,CAAC,EAAEA,CAAC;IAAEC,CAAC,EAAEA,CAAC;IAAEzB,KAAK,EAAEA,KAAK;IAAEC,MAAM,EAAEA,MAAM;IACxCS,GAAG,EAAEe,CAAC;IACNjB,KAAK,EAAEgB,CAAC,GAAGxB,KAAK;IAChBW,MAAM,EAAEV,MAAM,GAAGwB,CAAC;IAClBlB,IAAI,EAAEiB;EACV,CAAC,CAAC;EACF,OAAOI,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5C,cAAcA,CAACwC,CAAC,EAAEC,CAAC,EAAEzB,KAAK,EAAEC,MAAM,EAAE;EACzC,OAAO;IAAEuB,CAAC,EAAEA,CAAC;IAAEC,CAAC,EAAEA,CAAC;IAAEzB,KAAK,EAAEA,KAAK;IAAEC,MAAM,EAAEA;EAAO,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA,IAAI6B,iBAAiB,GAAG,aAAe,YAAY;EAC/C;AACJ;AACA;AACA;AACA;EACI,SAASA,iBAAiBA,CAACvD,MAAM,EAAE;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwD,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAGjD,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACT,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuD,iBAAiB,CAACzI,SAAS,CAAC6I,QAAQ,GAAG,YAAY;IAC/C,IAAIN,IAAI,GAAGN,cAAc,CAAC,IAAI,CAAC/C,MAAM,CAAC;IACtC,IAAI,CAAC0D,YAAY,GAAGL,IAAI;IACxB,OAAQA,IAAI,CAAC5B,KAAK,KAAK,IAAI,CAAC+B,cAAc,IACtCH,IAAI,CAAC3B,MAAM,KAAK,IAAI,CAAC+B,eAAe;EAC5C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIF,iBAAiB,CAACzI,SAAS,CAAC8I,aAAa,GAAG,YAAY;IACpD,IAAIP,IAAI,GAAG,IAAI,CAACK,YAAY;IAC5B,IAAI,CAACF,cAAc,GAAGH,IAAI,CAAC5B,KAAK;IAChC,IAAI,CAACgC,eAAe,GAAGJ,IAAI,CAAC3B,MAAM;IAClC,OAAO2B,IAAI;EACf,CAAC;EACD,OAAOE,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AAEJ,IAAIM,mBAAmB,GAAG,aAAe,YAAY;EACjD;AACJ;AACA;AACA;AACA;AACA;EACI,SAASA,mBAAmBA,CAAC7D,MAAM,EAAE8D,QAAQ,EAAE;IAC3C,IAAIC,WAAW,GAAGf,kBAAkB,CAACc,QAAQ,CAAC;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA/D,kBAAkB,CAAC,IAAI,EAAE;MAAEC,MAAM,EAAEA,MAAM;MAAE+D,WAAW,EAAEA;IAAY,CAAC,CAAC;EAC1E;EACA,OAAOF,mBAAmB;AAC9B,CAAC,CAAC,CAAE;AAEJ,IAAIG,iBAAiB,GAAG,aAAe,YAAY;EAC/C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,iBAAiBA,CAACpI,QAAQ,EAAEqI,UAAU,EAAEC,WAAW,EAAE;IAC1D;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAInK,OAAO,CAAC,CAAC;IAClC,IAAI,OAAO2B,QAAQ,KAAK,UAAU,EAAE;MAChC,MAAM,IAAIyI,SAAS,CAAC,yDAAyD,CAAC;IAClF;IACA,IAAI,CAACC,SAAS,GAAG1I,QAAQ;IACzB,IAAI,CAAC2I,WAAW,GAAGN,UAAU;IAC7B,IAAI,CAACO,YAAY,GAAGN,WAAW;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,iBAAiB,CAAClJ,SAAS,CAACqE,OAAO,GAAG,UAAUa,MAAM,EAAE;IACpD,IAAI,CAACe,SAAS,CAAC/F,MAAM,EAAE;MACnB,MAAM,IAAIqJ,SAAS,CAAC,0CAA0C,CAAC;IACnE;IACA;IACA,IAAI,OAAOI,OAAO,KAAK,WAAW,IAAI,EAAEA,OAAO,YAAY7J,MAAM,CAAC,EAAE;MAChE;IACJ;IACA,IAAI,EAAEoF,MAAM,YAAYI,WAAW,CAACJ,MAAM,CAAC,CAACyE,OAAO,CAAC,EAAE;MAClD,MAAM,IAAIJ,SAAS,CAAC,uCAAuC,CAAC;IAChE;IACA,IAAIK,YAAY,GAAG,IAAI,CAACN,aAAa;IACrC;IACA,IAAIM,YAAY,CAACjJ,GAAG,CAACuE,MAAM,CAAC,EAAE;MAC1B;IACJ;IACA0E,YAAY,CAACvJ,GAAG,CAAC6E,MAAM,EAAE,IAAIuD,iBAAiB,CAACvD,MAAM,CAAC,CAAC;IACvD,IAAI,CAACuE,WAAW,CAACnG,WAAW,CAAC,IAAI,CAAC;IAClC;IACA,IAAI,CAACmG,WAAW,CAACpG,OAAO,CAAC,CAAC;EAC9B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI6F,iBAAiB,CAAClJ,SAAS,CAAC6J,SAAS,GAAG,UAAU3E,MAAM,EAAE;IACtD,IAAI,CAACe,SAAS,CAAC/F,MAAM,EAAE;MACnB,MAAM,IAAIqJ,SAAS,CAAC,0CAA0C,CAAC;IACnE;IACA;IACA,IAAI,OAAOI,OAAO,KAAK,WAAW,IAAI,EAAEA,OAAO,YAAY7J,MAAM,CAAC,EAAE;MAChE;IACJ;IACA,IAAI,EAAEoF,MAAM,YAAYI,WAAW,CAACJ,MAAM,CAAC,CAACyE,OAAO,CAAC,EAAE;MAClD,MAAM,IAAIJ,SAAS,CAAC,uCAAuC,CAAC;IAChE;IACA,IAAIK,YAAY,GAAG,IAAI,CAACN,aAAa;IACrC;IACA,IAAI,CAACM,YAAY,CAACjJ,GAAG,CAACuE,MAAM,CAAC,EAAE;MAC3B;IACJ;IACA0E,YAAY,CAACpJ,MAAM,CAAC0E,MAAM,CAAC;IAC3B,IAAI,CAAC0E,YAAY,CAACzD,IAAI,EAAE;MACpB,IAAI,CAACsD,WAAW,CAAC/F,cAAc,CAAC,IAAI,CAAC;IACzC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIwF,iBAAiB,CAAClJ,SAAS,CAAC2E,UAAU,GAAG,YAAY;IACjD,IAAI,CAACmF,WAAW,CAAC,CAAC;IAClB,IAAI,CAACR,aAAa,CAAC1I,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAC6I,WAAW,CAAC/F,cAAc,CAAC,IAAI,CAAC;EACzC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIwF,iBAAiB,CAAClJ,SAAS,CAACiE,YAAY,GAAG,YAAY;IACnD,IAAI8F,KAAK,GAAG,IAAI;IAChB,IAAI,CAACD,WAAW,CAAC,CAAC;IAClB,IAAI,CAACR,aAAa,CAACzI,OAAO,CAAC,UAAUmJ,WAAW,EAAE;MAC9C,IAAIA,WAAW,CAACnB,QAAQ,CAAC,CAAC,EAAE;QACxBkB,KAAK,CAACV,mBAAmB,CAAC9I,IAAI,CAACyJ,WAAW,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACId,iBAAiB,CAAClJ,SAAS,CAACmE,eAAe,GAAG,YAAY;IACtD;IACA,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC,CAAC,EAAE;MACnB;IACJ;IACA,IAAInD,GAAG,GAAG,IAAI,CAAC2I,YAAY;IAC3B;IACA,IAAIjJ,OAAO,GAAG,IAAI,CAAC4I,mBAAmB,CAACY,GAAG,CAAC,UAAUD,WAAW,EAAE;MAC9D,OAAO,IAAIjB,mBAAmB,CAACiB,WAAW,CAAC9E,MAAM,EAAE8E,WAAW,CAAClB,aAAa,CAAC,CAAC,CAAC;IACnF,CAAC,CAAC;IACF,IAAI,CAACU,SAAS,CAACtI,IAAI,CAACH,GAAG,EAAEN,OAAO,EAAEM,GAAG,CAAC;IACtC,IAAI,CAAC+I,WAAW,CAAC,CAAC;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIZ,iBAAiB,CAAClJ,SAAS,CAAC8J,WAAW,GAAG,YAAY;IAClD,IAAI,CAACT,mBAAmB,CAAC3I,MAAM,CAAC,CAAC,CAAC;EACtC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIwI,iBAAiB,CAAClJ,SAAS,CAACkE,SAAS,GAAG,YAAY;IAChD,OAAO,IAAI,CAACmF,mBAAmB,CAACnJ,MAAM,GAAG,CAAC;EAC9C,CAAC;EACD,OAAOgJ,iBAAiB;AAC5B,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA;AACA,IAAIvF,SAAS,GAAG,OAAOuG,OAAO,KAAK,WAAW,GAAG,IAAIA,OAAO,CAAC,CAAC,GAAG,IAAI/K,OAAO,CAAC,CAAC;AAC9E;AACA;AACA;AACA;AACA,IAAIgL,cAAc,GAAG,aAAe,YAAY;EAC5C;AACJ;AACA;AACA;AACA;AACA;EACI,SAASA,cAAcA,CAACrJ,QAAQ,EAAE;IAC9B,IAAI,EAAE,IAAI,YAAYqJ,cAAc,CAAC,EAAE;MACnC,MAAM,IAAIZ,SAAS,CAAC,oCAAoC,CAAC;IAC7D;IACA,IAAI,CAACtD,SAAS,CAAC/F,MAAM,EAAE;MACnB,MAAM,IAAIqJ,SAAS,CAAC,0CAA0C,CAAC;IACnE;IACA,IAAIJ,UAAU,GAAGpG,wBAAwB,CAACgC,WAAW,CAAC,CAAC;IACvD,IAAIxB,QAAQ,GAAG,IAAI2F,iBAAiB,CAACpI,QAAQ,EAAEqI,UAAU,EAAE,IAAI,CAAC;IAChExF,SAAS,CAACtD,GAAG,CAAC,IAAI,EAAEkD,QAAQ,CAAC;EACjC;EACA,OAAO4G,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ;AACA,CACI,SAAS,EACT,WAAW,EACX,YAAY,CACf,CAACtJ,OAAO,CAAC,UAAUuJ,MAAM,EAAE;EACxBD,cAAc,CAACnK,SAAS,CAACoK,MAAM,CAAC,GAAG,YAAY;IAC3C,IAAInJ,EAAE;IACN,OAAO,CAACA,EAAE,GAAG0C,SAAS,CAAC1D,GAAG,CAAC,IAAI,CAAC,EAAEmK,MAAM,CAAC,CAACC,KAAK,CAACpJ,EAAE,EAAEgF,SAAS,CAAC;EAClE,CAAC;AACL,CAAC,CAAC;AAEF,IAAItG,KAAK,GAAI,YAAY;EACrB;EACA,IAAI,OAAO2B,QAAQ,CAAC6I,cAAc,KAAK,WAAW,EAAE;IAChD,OAAO7I,QAAQ,CAAC6I,cAAc;EAClC;EACA,OAAOA,cAAc;AACzB,CAAC,CAAE,CAAC;AAEJ,eAAexK,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}