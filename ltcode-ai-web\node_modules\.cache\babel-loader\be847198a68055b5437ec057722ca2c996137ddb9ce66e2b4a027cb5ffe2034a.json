{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport apiService from '@/services/apiService';\nexport default {\n  name: 'ChatView',\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      selectedApi: 'simple',\n      loading: false,\n      selectedFile: null,\n      textApis: [{\n        value: 'simple',\n        label: '简单聊天'\n      }, {\n        value: 'substrate',\n        label: '底层聊天'\n      }, {\n        value: 'assistant',\n        label: 'AI助手'\n      }, {\n        value: 'assistantSystem',\n        label: '系统助手'\n      }, {\n        value: 'assistantStream',\n        label: '流式助手'\n      }],\n      multimodalApis: [{\n        value: 'img2text',\n        label: '图片转文本'\n      }, {\n        value: 'img2text2',\n        label: '流式图片转文本'\n      }]\n    };\n  },\n  computed: {\n    needsFile() {\n      return this.multimodalApis.some(api => api.value === this.selectedApi);\n    },\n    canSend() {\n      const hasMessage = this.inputMessage.trim().length > 0;\n      const hasFileWhenNeeded = !this.needsFile || this.selectedFile;\n      return hasMessage && hasFileWhenNeeded && !this.loading;\n    }\n  },\n  methods: {\n    onApiChange() {\n      this.selectedFile = null;\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    handleFileChange(file) {\n      this.selectedFile = file.raw;\n    },\n    handleFileRemove() {\n      this.selectedFile = null;\n      return true;\n    },\n    async sendMessage() {\n      if (!this.canSend) return;\n      const userMessage = {\n        type: 'user',\n        content: this.inputMessage,\n        file: this.selectedFile,\n        timestamp: new Date()\n      };\n      this.messages.push(userMessage);\n      const assistantMessage = {\n        type: 'assistant',\n        content: '',\n        loading: true,\n        timestamp: new Date()\n      };\n      this.messages.push(assistantMessage);\n      this.loading = true;\n      this.scrollToBottom();\n      try {\n        const response = await apiService.sendMessage(this.selectedApi, this.inputMessage, this.selectedFile);\n        assistantMessage.loading = false;\n        assistantMessage.content = response;\n        this.inputMessage = '';\n        this.selectedFile = null;\n        if (this.$refs.upload) {\n          this.$refs.upload.clearFiles();\n        }\n      } catch (error) {\n        assistantMessage.loading = false;\n        assistantMessage.content = `错误: ${error.message}`;\n        this.$message.error('发送消息失败');\n      } finally {\n        this.loading = false;\n        this.scrollToBottom();\n      }\n    },\n    formatMessage(content) {\n      return content.replace(/\\n/g, '<br>');\n    },\n    scrollToBottom() {\n      this.$nextTick(() => {\n        const container = this.$refs.messagesContainer;\n        container.scrollTop = container.scrollHeight;\n      });\n    }\n  },\n  mounted() {\n    // 添加欢迎消息\n    this.messages.push({\n      type: 'assistant',\n      content: '您好！我是LTCode AI助手，请选择合适的接口开始对话。',\n      timestamp: new Date()\n    });\n  }\n};", "map": {"version": 3, "names": ["apiService", "name", "data", "messages", "inputMessage", "<PERSON><PERSON><PERSON>", "loading", "selectedFile", "textApis", "value", "label", "multimodalApis", "computed", "needsFile", "some", "api", "canSend", "hasMessage", "trim", "length", "hasFileWhenNeeded", "methods", "onApiChange", "$refs", "upload", "clearFiles", "handleFileChange", "file", "raw", "handleFileRemove", "sendMessage", "userMessage", "type", "content", "timestamp", "Date", "push", "assistant<PERSON><PERSON><PERSON>", "scrollToBottom", "response", "error", "message", "$message", "formatMessage", "replace", "$nextTick", "container", "messagesContainer", "scrollTop", "scrollHeight", "mounted"], "sources": ["src/views/ChatView.vue"], "sourcesContent": ["<template>\n  <div class=\"chat-container\">\n    <!-- 头部 -->\n    <div class=\"chat-header\">\n      <h1>LTCode AI Chat</h1>\n      <p>智能对话助手</p>\n    </div>\n\n    <!-- 消息区域 -->\n    <div class=\"chat-messages\" ref=\"messagesContainer\">\n      <div \n        v-for=\"(message, index) in messages\" \n        :key=\"index\" \n        :class=\"['message-item', message.type]\"\n      >\n        <div class=\"message-content\">\n          <div v-if=\"message.type === 'user' && message.file\" class=\"file-info\">\n            <i class=\"el-icon-picture\"></i>\n            {{ message.file.name }}\n          </div>\n          <div v-html=\"formatMessage(message.content)\"></div>\n          <div v-if=\"message.loading\" class=\"loading\">\n            <i class=\"el-icon-loading\"></i> 正在思考...\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 输入区域 -->\n    <div class=\"chat-input-area\">\n      <!-- API选择器 -->\n      <div class=\"api-selector\">\n        <el-select \n          v-model=\"selectedApi\" \n          placeholder=\"选择API接口\"\n          size=\"small\"\n          @change=\"onApiChange\"\n        >\n          <el-option-group label=\"文本聊天接口\">\n            <el-option\n              v-for=\"api in textApis\"\n              :key=\"api.value\"\n              :label=\"api.label\"\n              :value=\"api.value\"\n            />\n          </el-option-group>\n          <el-option-group label=\"多模态接口\">\n            <el-option\n              v-for=\"api in multimodalApis\"\n              :key=\"api.value\"\n              :label=\"api.label\"\n              :value=\"api.value\"\n            />\n          </el-option-group>\n        </el-select>\n      </div>\n\n      <!-- 文件上传 -->\n      <div v-if=\"needsFile\" class=\"file-upload\">\n        <el-upload\n          ref=\"upload\"\n          :auto-upload=\"false\"\n          :show-file-list=\"true\"\n          :on-change=\"handleFileChange\"\n          :before-remove=\"handleFileRemove\"\n          accept=\"image/*\"\n          drag\n        >\n          <i class=\"el-icon-upload\"></i>\n          <div class=\"el-upload__text\">将图片拖到此处，或<em>点击上传</em></div>\n          <div class=\"el-upload__tip\" slot=\"tip\">只能上传jpg/png文件，且不超过10MB</div>\n        </el-upload>\n      </div>\n\n      <!-- 输入框和发送按钮 -->\n      <div class=\"input-container\">\n        <el-input\n          v-model=\"inputMessage\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入您的消息... (Enter发送，Shift+Enter换行)\"\n          @keydown.enter.exact=\"handleEnterKey\"\n          @keydown.enter.shift=\"handleShiftEnter\"\n          @keydown.enter.ctrl=\"sendMessage\"\n          :disabled=\"loading\"\n        />\n        <el-button \n          type=\"primary\" \n          @click=\"sendMessage\"\n          :loading=\"loading\"\n          :disabled=\"!canSend\"\n          size=\"large\"\n        >\n          发送\n        </el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport apiService from '@/services/apiService'\n\nexport default {\n  name: 'ChatView',\n  data() {\n    return {\n      messages: [],\n      inputMessage: '',\n      selectedApi: 'simple',\n      loading: false,\n      selectedFile: null,\n      textApis: [\n        { value: 'simple', label: '简单聊天' },\n        { value: 'substrate', label: '底层聊天' },\n        { value: 'assistant', label: 'AI助手' },\n        { value: 'assistantSystem', label: '系统助手' },\n        { value: 'assistantStream', label: '流式助手' }\n      ],\n      multimodalApis: [\n        { value: 'img2text', label: '图片转文本' },\n        { value: 'img2text2', label: '流式图片转文本' }\n      ]\n    }\n  },\n  computed: {\n    needsFile() {\n      return this.multimodalApis.some(api => api.value === this.selectedApi)\n    },\n    canSend() {\n      const hasMessage = this.inputMessage.trim().length > 0\n      const hasFileWhenNeeded = !this.needsFile || this.selectedFile\n      return hasMessage && hasFileWhenNeeded && !this.loading\n    }\n  },\n  methods: {\n    onApiChange() {\n      this.selectedFile = null\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles()\n      }\n    },\n    handleFileChange(file) {\n      this.selectedFile = file.raw\n    },\n    handleFileRemove() {\n      this.selectedFile = null\n      return true\n    },\n    async sendMessage() {\n      if (!this.canSend) return\n\n      const userMessage = {\n        type: 'user',\n        content: this.inputMessage,\n        file: this.selectedFile,\n        timestamp: new Date()\n      }\n\n      this.messages.push(userMessage)\n      \n      const assistantMessage = {\n        type: 'assistant',\n        content: '',\n        loading: true,\n        timestamp: new Date()\n      }\n      this.messages.push(assistantMessage)\n\n      this.loading = true\n      this.scrollToBottom()\n\n      try {\n        const response = await apiService.sendMessage(\n          this.selectedApi,\n          this.inputMessage,\n          this.selectedFile\n        )\n\n        assistantMessage.loading = false\n        assistantMessage.content = response\n\n        this.inputMessage = ''\n        this.selectedFile = null\n        if (this.$refs.upload) {\n          this.$refs.upload.clearFiles()\n        }\n      } catch (error) {\n        assistantMessage.loading = false\n        assistantMessage.content = `错误: ${error.message}`\n        this.$message.error('发送消息失败')\n      } finally {\n        this.loading = false\n        this.scrollToBottom()\n      }\n    },\n    formatMessage(content) {\n      return content.replace(/\\n/g, '<br>')\n    },\n    scrollToBottom() {\n      this.$nextTick(() => {\n        const container = this.$refs.messagesContainer\n        container.scrollTop = container.scrollHeight\n      })\n    }\n  },\n  mounted() {\n    // 添加欢迎消息\n    this.messages.push({\n      type: 'assistant',\n      content: '您好！我是LTCode AI助手，请选择合适的接口开始对话。',\n      timestamp: new Date()\n    })\n  }\n}\n</script>\n\n<style scoped>\n.loading {\n  margin-top: 8px;\n  color: #999;\n  font-size: 12px;\n}\n\n.file-info {\n  margin-bottom: 8px;\n  padding: 8px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  font-size: 12px;\n}\n\n.file-info i {\n  margin-right: 5px;\n}\n</style>\n"], "mappings": ";;;AAqGA,OAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,YAAA;MACAC,WAAA;MACAC,OAAA;MACAC,YAAA;MACAC,QAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,cAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAE,QAAA;IACAC,UAAA;MACA,YAAAF,cAAA,CAAAG,IAAA,CAAAC,GAAA,IAAAA,GAAA,CAAAN,KAAA,UAAAJ,WAAA;IACA;IACAW,QAAA;MACA,MAAAC,UAAA,QAAAb,YAAA,CAAAc,IAAA,GAAAC,MAAA;MACA,MAAAC,iBAAA,SAAAP,SAAA,SAAAN,YAAA;MACA,OAAAU,UAAA,IAAAG,iBAAA,UAAAd,OAAA;IACA;EACA;EACAe,OAAA;IACAC,YAAA;MACA,KAAAf,YAAA;MACA,SAAAgB,KAAA,CAAAC,MAAA;QACA,KAAAD,KAAA,CAAAC,MAAA,CAAAC,UAAA;MACA;IACA;IACAC,iBAAAC,IAAA;MACA,KAAApB,YAAA,GAAAoB,IAAA,CAAAC,GAAA;IACA;IACAC,iBAAA;MACA,KAAAtB,YAAA;MACA;IACA;IACA,MAAAuB,YAAA;MACA,UAAAd,OAAA;MAEA,MAAAe,WAAA;QACAC,IAAA;QACAC,OAAA,OAAA7B,YAAA;QACAuB,IAAA,OAAApB,YAAA;QACA2B,SAAA,MAAAC,IAAA;MACA;MAEA,KAAAhC,QAAA,CAAAiC,IAAA,CAAAL,WAAA;MAEA,MAAAM,gBAAA;QACAL,IAAA;QACAC,OAAA;QACA3B,OAAA;QACA4B,SAAA,MAAAC,IAAA;MACA;MACA,KAAAhC,QAAA,CAAAiC,IAAA,CAAAC,gBAAA;MAEA,KAAA/B,OAAA;MACA,KAAAgC,cAAA;MAEA;QACA,MAAAC,QAAA,SAAAvC,UAAA,CAAA8B,WAAA,CACA,KAAAzB,WAAA,EACA,KAAAD,YAAA,EACA,KAAAG,YACA;QAEA8B,gBAAA,CAAA/B,OAAA;QACA+B,gBAAA,CAAAJ,OAAA,GAAAM,QAAA;QAEA,KAAAnC,YAAA;QACA,KAAAG,YAAA;QACA,SAAAgB,KAAA,CAAAC,MAAA;UACA,KAAAD,KAAA,CAAAC,MAAA,CAAAC,UAAA;QACA;MACA,SAAAe,KAAA;QACAH,gBAAA,CAAA/B,OAAA;QACA+B,gBAAA,CAAAJ,OAAA,UAAAO,KAAA,CAAAC,OAAA;QACA,KAAAC,QAAA,CAAAF,KAAA;MACA;QACA,KAAAlC,OAAA;QACA,KAAAgC,cAAA;MACA;IACA;IACAK,cAAAV,OAAA;MACA,OAAAA,OAAA,CAAAW,OAAA;IACA;IACAN,eAAA;MACA,KAAAO,SAAA;QACA,MAAAC,SAAA,QAAAvB,KAAA,CAAAwB,iBAAA;QACAD,SAAA,CAAAE,SAAA,GAAAF,SAAA,CAAAG,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAA/C,QAAA,CAAAiC,IAAA;MACAJ,IAAA;MACAC,OAAA;MACAC,SAAA,MAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}