{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport ChatView from '../views/ChatView.vue';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  name: 'Chat',\n  component: ChatView\n}];\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChatView", "use", "routes", "path", "name", "component", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport ChatView from '../views/ChatView.vue'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Chat',\n    component: ChatView\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,uBAAuB;AAE5CF,GAAG,CAACG,GAAG,CAACF,SAAS,CAAC;AAElB,MAAMG,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEL;AACb,CAAC,CACF;AAED,MAAMM,MAAM,GAAG,IAAIP,SAAS,CAAC;EAC3BQ,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BT;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}