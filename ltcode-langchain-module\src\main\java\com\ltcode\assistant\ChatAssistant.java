package com.ltcode.assistant;

import dev.langchain4j.service.SystemMessage;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author： ltcode
 * @Create by： 2025/8/7 0007 21:07
 * @ClassName: ChatAssistant
 * @Describe：
 */
public interface ChatAssistant {

    /**
     * 聊天
     * @param question 问题
     * @return 回答
     */
    String chat(String question);

    /**
     * SystemMessage 系统提示词
     * @param question
     * @return
     * @SystemMessage 系统提示词是设置 AI 模型行为规则和角色定位的隐藏指令，用户通常不能直接看到
     *  通常，作为开发人员，您应该定义此消息的内容。 通常，你会在这里写下关于 LLM 在这次对话中的角色的说明， 它应该如何表现，以什么风格回答等等。
     *  LLM 经过训练，比其他类型的消息更关注， 所以要小心，最好不要给最终用户免费定义或注入一些输入的权限。 通常，它位于对话的开头
     */
    @SystemMessage("你是一名小红书文案助手，擅长使用Emoji风格编辑文案。每篇文案包含引人入胜的标题、每个段落开始和结尾均为Emoji表情结尾，并保持原文的意思。")
    String chatWithSystemMessage(String question);
    Flux<String> assistantStream(String question);
}
