# LTCode AI Web 项目说明

## 项目概述

这是一个基于Vue2的智能聊天前端项目，专门为ltcode-langchain-module后端服务设计。项目实现了美观的聊天界面，支持多种AI模型接口调用，包括文本聊天和多模态聊天功能。

## 功能特性

### ✅ 已实现功能

1. **响应式聊天界面**
   - 美观简洁的设计风格
   - 支持移动端和桌面端
   - 渐变背景和毛玻璃效果
   - 消息气泡动画效果

2. **接口切换功能**
   - 左下角接口选择器
   - 支持7种不同的后端接口
   - 自动适配文本和文件输入

3. **多模态支持**
   - 文本消息发送
   - 图片文件上传
   - 拖拽上传功能
   - 文件类型验证

4. **流式响应支持**
   - SSE (Server-Sent Events) 支持
   - 实时消息流显示
   - 连接状态管理

5. **用户体验优化**
   - 加载状态显示
   - 错误处理机制
   - 自动滚动到底部
   - 快捷键支持 (Ctrl+Enter)

## 支持的后端接口

### 文本聊天接口 (ChatDemoController)
1. **简单聊天** - `GET /chat/simple/demo/{msg}`
2. **底层聊天** - `GET /chat/substrate/demo/{msg}`
3. **AI助手** - `GET /chat/assistant/demo/{msg}`
4. **系统助手** - `GET /chat/assistantSystem/demo/{msg}`
5. **流式助手** - `GET /chat/assistantStream/demo/{msg}` (SSE)

### 多模态接口 (ChatMultimodalityDemoController)
1. **图片转文本** - `POST /multi/img2text` (需要文件)
2. **流式图片转文本** - `POST /multi/img2text2` (需要文件)

## 技术栈

- **前端框架**: Vue 2.6.14
- **UI组件库**: Element UI 2.15.14
- **HTTP客户端**: Axios 1.6.0
- **路由管理**: Vue Router 3.5.4
- **构建工具**: Vue CLI 5.0.8
- **代码规范**: ESLint + Babel

## 项目结构

```
ltcode-ai-web/
├── public/
│   └── index.html              # HTML模板
├── src/
│   ├── assets/
│   │   └── css/
│   │       └── global.css      # 全局样式
│   ├── services/
│   │   └── apiService.js       # API服务封装
│   ├── views/
│   │   └── ChatView.vue        # 主聊天界面
│   ├── router/
│   │   └── index.js            # 路由配置
│   ├── App.vue                 # 根组件
│   └── main.js                 # 入口文件
├── package.json                # 依赖配置
├── vue.config.js              # Vue配置
├── babel.config.js            # Babel配置
├── .eslintrc.js              # ESLint配置
└── README.md                  # 项目文档
```

## 安装和运行

### 环境要求
- Node.js >= 14.0.0
- npm 或 yarn

### 安装依赖
```bash
cd ltcode-ai-web
npm install
```

### 开发环境运行
```bash
npm run serve
```
访问: http://localhost:8080

### 生产环境构建
```bash
npm run build
```

## 配置说明

### 后端代理配置
项目已配置代理，将 `/api` 路径代理到后端服务 `http://localhost:8001`

如需修改后端地址，编辑 `vue.config.js`:
```javascript
devServer: {
  proxy: {
    '/api': {
      target: 'http://your-backend-url:port',
      changeOrigin: true,
      pathRewrite: {
        '^/api': ''
      }
    }
  }
}
```

## 使用说明

1. **启动后端服务**: 确保ltcode-langchain-module服务运行在8001端口
2. **启动前端服务**: 运行 `npm run serve`
3. **选择接口**: 在左下角选择要使用的API接口
4. **发送消息**: 
   - 文本接口: 直接输入文本消息
   - 多模态接口: 先上传图片，再输入描述文本
5. **发送方式**: 点击发送按钮或使用 Ctrl+Enter 快捷键

## 样式特色

- **渐变背景**: 紫蓝色渐变背景
- **毛玻璃效果**: 半透明背景模糊效果
- **消息气泡**: 用户消息（右侧，紫色渐变）和AI回复（左侧，白色）
- **动画效果**: 消息出现动画、按钮悬停效果
- **响应式设计**: 适配手机、平板、桌面端

## 注意事项

1. 确保后端服务已启动并运行在8001端口
2. 文件上传仅支持图片格式（jpg, png等）
3. 文件大小限制为10MB
4. 流式接口可能需要较长时间响应
5. 建议使用现代浏览器以获得最佳体验

## 开发扩展

如需自定义或扩展功能：

1. **添加新接口**: 在 `apiService.js` 中添加新的API方法
2. **修改样式**: 编辑 `global.css` 或组件内的样式
3. **添加新功能**: 在 `ChatView.vue` 中扩展组件功能
4. **修改配置**: 编辑相应的配置文件

## 许可证

MIT License
