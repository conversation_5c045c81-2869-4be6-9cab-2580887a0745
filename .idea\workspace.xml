<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="02c4d570-7e3a-4746-8f24-364685baa9c7" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/ltcode-langchain-module/src/main/java/com/ltcode/assistant/ChatAssistant.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ltcode-langchain-module/src/main/java/com/ltcode/config/CorsFilter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ltcode-langchain-module/src/main/java/com/ltcode/config/LLMConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ltcode-langchain-module/src/main/java/com/ltcode/controller/ChatDemoController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ltcode-langchain-module/src/main/java/com/ltcode/controller/ChatMultimodalityDemoController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ltcode-langchain-module/src/main/java/com/ltcode/listener/ChatModeCustomerListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ltcode-langchain-module/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ltcode-langchain-module/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ltcode-langchain-module/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30p9gy1NqSybJSDpy0yF6wosHn8" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/space/workspace/ltcode_ai_module/ltcode-langchain-module/src/main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;shared-indexes&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\space\workspace\ltcode_ai_module\ltcode-langchain-module\src\main" />
      <recent name="D:\space\workspace\ltcode_ai_module\ltcode-langchain-module\src\main\java" />
      <recent name="D:\space\workspace\ltcode_ai_module" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.ltcode.config" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="LangChainModule" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="$PROJECT_DIR$/../../../tools/jdk17/openjdk-17.0.1/jdk-17.0.1" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="ltcode-langchain-module" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ltcode.LangChainModule" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ltcode.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.LangChainModule" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="02c4d570-7e3a-4746-8f24-364685baa9c7" name="Changes" comment="" />
      <created>1754313761758</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754313761758</updated>
      <workItem from="1754313762763" duration="2718000" />
      <workItem from="1754400948104" duration="381000" />
      <workItem from="1754401346777" duration="2437000" />
      <workItem from="1754483360791" duration="2282000" />
      <workItem from="1754571548143" duration="4604000" />
      <workItem from="1754915377856" duration="8863000" />
      <workItem from="1755002919254" duration="1258000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>