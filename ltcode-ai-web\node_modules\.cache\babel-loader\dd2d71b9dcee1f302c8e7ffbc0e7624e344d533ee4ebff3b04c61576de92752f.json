{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nrequire(\"core-js/modules/es.iterator.reduce.js\");\nvar isMergeableObject = function isMergeableObject(value) {\n  return isNonNullObject(value) && !isSpecial(value);\n};\nfunction isNonNullObject(value) {\n  return !!value && typeof value === 'object';\n}\nfunction isSpecial(value) {\n  var stringValue = Object.prototype.toString.call(value);\n  return stringValue === '[object RegExp]' || stringValue === '[object Date]' || isReactElement(value);\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\nfunction isReactElement(value) {\n  return value.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction emptyTarget(val) {\n  return Array.isArray(val) ? [] : {};\n}\nfunction cloneIfNecessary(value, optionsArgument) {\n  var clone = optionsArgument && optionsArgument.clone === true;\n  return clone && isMergeableObject(value) ? deepmerge(emptyTarget(value), value, optionsArgument) : value;\n}\nfunction defaultArrayMerge(target, source, optionsArgument) {\n  var destination = target.slice();\n  source.forEach(function (e, i) {\n    if (typeof destination[i] === 'undefined') {\n      destination[i] = cloneIfNecessary(e, optionsArgument);\n    } else if (isMergeableObject(e)) {\n      destination[i] = deepmerge(target[i], e, optionsArgument);\n    } else if (target.indexOf(e) === -1) {\n      destination.push(cloneIfNecessary(e, optionsArgument));\n    }\n  });\n  return destination;\n}\nfunction mergeObject(target, source, optionsArgument) {\n  var destination = {};\n  if (isMergeableObject(target)) {\n    Object.keys(target).forEach(function (key) {\n      destination[key] = cloneIfNecessary(target[key], optionsArgument);\n    });\n  }\n  Object.keys(source).forEach(function (key) {\n    if (!isMergeableObject(source[key]) || !target[key]) {\n      destination[key] = cloneIfNecessary(source[key], optionsArgument);\n    } else {\n      destination[key] = deepmerge(target[key], source[key], optionsArgument);\n    }\n  });\n  return destination;\n}\nfunction deepmerge(target, source, optionsArgument) {\n  var sourceIsArray = Array.isArray(source);\n  var targetIsArray = Array.isArray(target);\n  var options = optionsArgument || {\n    arrayMerge: defaultArrayMerge\n  };\n  var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n  if (!sourceAndTargetTypesMatch) {\n    return cloneIfNecessary(source, optionsArgument);\n  } else if (sourceIsArray) {\n    var arrayMerge = options.arrayMerge || defaultArrayMerge;\n    return arrayMerge(target, source, optionsArgument);\n  } else {\n    return mergeObject(target, source, optionsArgument);\n  }\n}\ndeepmerge.all = function deepmergeAll(array, optionsArgument) {\n  if (!Array.isArray(array) || array.length < 2) {\n    throw new Error('first argument should be an array with at least two elements');\n  }\n\n  // we are sure there are at least 2 values, so it is safe to have no initial value\n  return array.reduce(function (prev, next) {\n    return deepmerge(prev, next, optionsArgument);\n  });\n};\nvar deepmerge_1 = deepmerge;\nmodule.exports = deepmerge_1;", "map": {"version": 3, "names": ["require", "isMergeableObject", "value", "isNonNullObject", "isSpecial", "stringValue", "Object", "prototype", "toString", "call", "isReactElement", "canUseSymbol", "Symbol", "for", "REACT_ELEMENT_TYPE", "$$typeof", "emptyTarget", "val", "Array", "isArray", "cloneIfNecessary", "optionsArgument", "clone", "deepmerge", "defaultArrayMerge", "target", "source", "destination", "slice", "for<PERSON>ach", "e", "i", "indexOf", "push", "mergeObject", "keys", "key", "sourceIsArray", "targetIsArray", "options", "arrayMerge", "sourceAndTargetTypesMatch", "all", "deepmergeAll", "array", "length", "Error", "reduce", "prev", "next", "deepmerge_1", "module", "exports"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/deepmerge/dist/cjs.js"], "sourcesContent": ["'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n    return Array.isArray(val) ? [] : {}\n}\n\nfunction cloneIfNecessary(value, optionsArgument) {\n    var clone = optionsArgument && optionsArgument.clone === true;\n    return (clone && isMergeableObject(value)) ? deepmerge(emptyTarget(value), value, optionsArgument) : value\n}\n\nfunction defaultArrayMerge(target, source, optionsArgument) {\n    var destination = target.slice();\n    source.forEach(function(e, i) {\n        if (typeof destination[i] === 'undefined') {\n            destination[i] = cloneIfNecessary(e, optionsArgument);\n        } else if (isMergeableObject(e)) {\n            destination[i] = deepmerge(target[i], e, optionsArgument);\n        } else if (target.indexOf(e) === -1) {\n            destination.push(cloneIfNecessary(e, optionsArgument));\n        }\n    });\n    return destination\n}\n\nfunction mergeObject(target, source, optionsArgument) {\n    var destination = {};\n    if (isMergeableObject(target)) {\n        Object.keys(target).forEach(function(key) {\n            destination[key] = cloneIfNecessary(target[key], optionsArgument);\n        });\n    }\n    Object.keys(source).forEach(function(key) {\n        if (!isMergeableObject(source[key]) || !target[key]) {\n            destination[key] = cloneIfNecessary(source[key], optionsArgument);\n        } else {\n            destination[key] = deepmerge(target[key], source[key], optionsArgument);\n        }\n    });\n    return destination\n}\n\nfunction deepmerge(target, source, optionsArgument) {\n    var sourceIsArray = Array.isArray(source);\n    var targetIsArray = Array.isArray(target);\n    var options = optionsArgument || { arrayMerge: defaultArrayMerge };\n    var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n    if (!sourceAndTargetTypesMatch) {\n        return cloneIfNecessary(source, optionsArgument)\n    } else if (sourceIsArray) {\n        var arrayMerge = options.arrayMerge || defaultArrayMerge;\n        return arrayMerge(target, source, optionsArgument)\n    } else {\n        return mergeObject(target, source, optionsArgument)\n    }\n}\n\ndeepmerge.all = function deepmergeAll(array, optionsArgument) {\n    if (!Array.isArray(array) || array.length < 2) {\n        throw new Error('first argument should be an array with at least two elements')\n    }\n\n    // we are sure there are at least 2 values, so it is safe to have no initial value\n    return array.reduce(function(prev, next) {\n        return deepmerge(prev, next, optionsArgument)\n    })\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEb,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;EACzD,OAAOC,eAAe,CAACD,KAAK,CAAC,IACzB,CAACE,SAAS,CAACF,KAAK,CAAC;AACtB,CAAC;AAED,SAASC,eAAeA,CAACD,KAAK,EAAE;EAC/B,OAAO,CAAC,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ;AAC5C;AAEA,SAASE,SAASA,CAACF,KAAK,EAAE;EACzB,IAAIG,WAAW,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC;EAEvD,OAAOG,WAAW,KAAK,iBAAiB,IACpCA,WAAW,KAAK,eAAe,IAC/BK,cAAc,CAACR,KAAK,CAAC;AAC1B;;AAEA;AACA,IAAIS,YAAY,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG;AAC7D,IAAIC,kBAAkB,GAAGH,YAAY,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM;AAE5E,SAASH,cAAcA,CAACR,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACa,QAAQ,KAAKD,kBAAkB;AAC7C;AAEA,SAASE,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACvC;AAEA,SAASG,gBAAgBA,CAAClB,KAAK,EAAEmB,eAAe,EAAE;EAC9C,IAAIC,KAAK,GAAGD,eAAe,IAAIA,eAAe,CAACC,KAAK,KAAK,IAAI;EAC7D,OAAQA,KAAK,IAAIrB,iBAAiB,CAACC,KAAK,CAAC,GAAIqB,SAAS,CAACP,WAAW,CAACd,KAAK,CAAC,EAAEA,KAAK,EAAEmB,eAAe,CAAC,GAAGnB,KAAK;AAC9G;AAEA,SAASsB,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAEL,eAAe,EAAE;EACxD,IAAIM,WAAW,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC;EAChCF,MAAM,CAACG,OAAO,CAAC,UAASC,CAAC,EAAEC,CAAC,EAAE;IAC1B,IAAI,OAAOJ,WAAW,CAACI,CAAC,CAAC,KAAK,WAAW,EAAE;MACvCJ,WAAW,CAACI,CAAC,CAAC,GAAGX,gBAAgB,CAACU,CAAC,EAAET,eAAe,CAAC;IACzD,CAAC,MAAM,IAAIpB,iBAAiB,CAAC6B,CAAC,CAAC,EAAE;MAC7BH,WAAW,CAACI,CAAC,CAAC,GAAGR,SAAS,CAACE,MAAM,CAACM,CAAC,CAAC,EAAED,CAAC,EAAET,eAAe,CAAC;IAC7D,CAAC,MAAM,IAAII,MAAM,CAACO,OAAO,CAACF,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCH,WAAW,CAACM,IAAI,CAACb,gBAAgB,CAACU,CAAC,EAAET,eAAe,CAAC,CAAC;IAC1D;EACJ,CAAC,CAAC;EACF,OAAOM,WAAW;AACtB;AAEA,SAASO,WAAWA,CAACT,MAAM,EAAEC,MAAM,EAAEL,eAAe,EAAE;EAClD,IAAIM,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI1B,iBAAiB,CAACwB,MAAM,CAAC,EAAE;IAC3BnB,MAAM,CAAC6B,IAAI,CAACV,MAAM,CAAC,CAACI,OAAO,CAAC,UAASO,GAAG,EAAE;MACtCT,WAAW,CAACS,GAAG,CAAC,GAAGhB,gBAAgB,CAACK,MAAM,CAACW,GAAG,CAAC,EAAEf,eAAe,CAAC;IACrE,CAAC,CAAC;EACN;EACAf,MAAM,CAAC6B,IAAI,CAACT,MAAM,CAAC,CAACG,OAAO,CAAC,UAASO,GAAG,EAAE;IACtC,IAAI,CAACnC,iBAAiB,CAACyB,MAAM,CAACU,GAAG,CAAC,CAAC,IAAI,CAACX,MAAM,CAACW,GAAG,CAAC,EAAE;MACjDT,WAAW,CAACS,GAAG,CAAC,GAAGhB,gBAAgB,CAACM,MAAM,CAACU,GAAG,CAAC,EAAEf,eAAe,CAAC;IACrE,CAAC,MAAM;MACHM,WAAW,CAACS,GAAG,CAAC,GAAGb,SAAS,CAACE,MAAM,CAACW,GAAG,CAAC,EAAEV,MAAM,CAACU,GAAG,CAAC,EAAEf,eAAe,CAAC;IAC3E;EACJ,CAAC,CAAC;EACF,OAAOM,WAAW;AACtB;AAEA,SAASJ,SAASA,CAACE,MAAM,EAAEC,MAAM,EAAEL,eAAe,EAAE;EAChD,IAAIgB,aAAa,GAAGnB,KAAK,CAACC,OAAO,CAACO,MAAM,CAAC;EACzC,IAAIY,aAAa,GAAGpB,KAAK,CAACC,OAAO,CAACM,MAAM,CAAC;EACzC,IAAIc,OAAO,GAAGlB,eAAe,IAAI;IAAEmB,UAAU,EAAEhB;EAAkB,CAAC;EAClE,IAAIiB,yBAAyB,GAAGJ,aAAa,KAAKC,aAAa;EAE/D,IAAI,CAACG,yBAAyB,EAAE;IAC5B,OAAOrB,gBAAgB,CAACM,MAAM,EAAEL,eAAe,CAAC;EACpD,CAAC,MAAM,IAAIgB,aAAa,EAAE;IACtB,IAAIG,UAAU,GAAGD,OAAO,CAACC,UAAU,IAAIhB,iBAAiB;IACxD,OAAOgB,UAAU,CAACf,MAAM,EAAEC,MAAM,EAAEL,eAAe,CAAC;EACtD,CAAC,MAAM;IACH,OAAOa,WAAW,CAACT,MAAM,EAAEC,MAAM,EAAEL,eAAe,CAAC;EACvD;AACJ;AAEAE,SAAS,CAACmB,GAAG,GAAG,SAASC,YAAYA,CAACC,KAAK,EAAEvB,eAAe,EAAE;EAC1D,IAAI,CAACH,KAAK,CAACC,OAAO,CAACyB,KAAK,CAAC,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IAC3C,MAAM,IAAIC,KAAK,CAAC,8DAA8D,CAAC;EACnF;;EAEA;EACA,OAAOF,KAAK,CAACG,MAAM,CAAC,UAASC,IAAI,EAAEC,IAAI,EAAE;IACrC,OAAO1B,SAAS,CAACyB,IAAI,EAAEC,IAAI,EAAE5B,eAAe,CAAC;EACjD,CAAC,CAAC;AACN,CAAC;AAED,IAAI6B,WAAW,GAAG3B,SAAS;AAE3B4B,MAAM,CAACC,OAAO,GAAGF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}