{"ast": null, "code": "module.exports = {\n  \"default\": require(\"core-js/library/fn/object/assign\"),\n  __esModule: true\n};", "map": {"version": 3, "names": ["module", "exports", "require", "__esModule"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/babel-runtime/core-js/object/assign.js"], "sourcesContent": ["module.exports = { \"default\": require(\"core-js/library/fn/object/assign\"), __esModule: true };"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EAAE,SAAS,EAAEC,OAAO,CAAC,kCAAkC,CAAC;EAAEC,UAAU,EAAE;AAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}