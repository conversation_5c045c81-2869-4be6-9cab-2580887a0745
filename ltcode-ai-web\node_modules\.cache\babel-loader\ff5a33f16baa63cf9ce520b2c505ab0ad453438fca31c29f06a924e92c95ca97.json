{"ast": null, "code": "import axios from 'axios';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: 'http://localhost:8001',\n  timeout: 300000,\n  // 5分钟超时，适应流式响应\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  console.log('发送请求:', config);\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  console.log('收到响应:', response);\n  return response;\n}, error => {\n  console.error('请求错误:', error);\n  return Promise.reject(error);\n});\nclass ApiService {\n  /**\n   * 发送消息到后端\n   * @param {string} apiType - API类型\n   * @param {string} message - 消息内容\n   * @param {File} file - 文件（可选）\n   * @returns {Promise<string>} 响应内容\n   */\n  async sendMessage(apiType, message, file = null) {\n    try {\n      switch (apiType) {\n        case 'simple':\n          return await this.simpleChat(message);\n        case 'substrate':\n          return await this.substrateChat(message);\n        case 'assistant':\n          return await this.assistantChat(message);\n        case 'assistantSystem':\n          return await this.assistantSystemChat(message);\n        case 'assistantStream':\n          return await this.assistantStreamChat(message);\n        case 'img2text':\n          return await this.img2text(message, file);\n        case 'img2text2':\n          return await this.img2textStream(message, file);\n        default:\n          throw new Error('未知的API类型');\n      }\n    } catch (error) {\n      console.error('API调用失败:', error);\n      throw new Error(error.response?.data?.message || error.message || '请求失败');\n    }\n  }\n\n  /**\n   * 简单聊天\n   */\n  async simpleChat(message) {\n    const response = await api.get(`/chat/simple/demo/${encodeURIComponent(message)}`);\n    return response.data;\n  }\n\n  /**\n   * 底层聊天\n   */\n  async substrateChat(message) {\n    const response = await api.get(`/chat/substrate/demo/${encodeURIComponent(message)}`);\n    return response.data;\n  }\n\n  /**\n   * AI助手聊天\n   */\n  async assistantChat(message) {\n    const response = await api.get(`/chat/assistant/demo/${encodeURIComponent(message)}`);\n    return response.data;\n  }\n\n  /**\n   * 系统助手聊天\n   */\n  async assistantSystemChat(message) {\n    const response = await api.get(`/chat/assistantSystem/demo/${encodeURIComponent(message)}`);\n    return response.data;\n  }\n\n  /**\n   * 流式助手聊天\n   */\n  async assistantStreamChat(message) {\n    return new Promise((resolve, reject) => {\n      const eventSource = new EventSource(`/api/chat/assistantStream/demo/${encodeURIComponent(message)}`);\n      let result = '';\n      eventSource.onmessage = function (event) {\n        if (event.data) {\n          result += event.data;\n        }\n      };\n      eventSource.onerror = function (error) {\n        console.error('SSE错误:', error);\n        eventSource.close();\n        if (result) {\n          resolve(result);\n        } else {\n          reject(new Error('流式请求失败'));\n        }\n      };\n      eventSource.onopen = function () {\n        console.log('SSE连接已建立');\n      };\n\n      // 设置超时\n      setTimeout(() => {\n        eventSource.close();\n        if (result) {\n          resolve(result);\n        } else {\n          reject(new Error('请求超时'));\n        }\n      }, 300000); // 5分钟超时\n    });\n  }\n\n  /**\n   * 图片转文本\n   */\n  async img2text(message, file) {\n    if (!file) {\n      throw new Error('请选择图片文件');\n    }\n    const formData = new FormData();\n    formData.append('msg', message);\n    formData.append('file', file);\n    const response = await api.post('/multi/img2text', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  }\n\n  /**\n   * 流式图片转文本\n   */\n  async img2textStream(message, file) {\n    if (!file) {\n      throw new Error('请选择图片文件');\n    }\n    const formData = new FormData();\n    formData.append('msg', message);\n    formData.append('file', file);\n    const response = await api.post('/multi/img2text2', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  }\n}\nexport default new ApiService();", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "console", "log", "error", "Promise", "reject", "response", "ApiService", "sendMessage", "apiType", "message", "file", "simpleChat", "substrateChat", "<PERSON><PERSON><PERSON>", "assistantSystem<PERSON><PERSON>", "assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "img2text", "img2textStream", "Error", "data", "get", "encodeURIComponent", "resolve", "eventSource", "EventSource", "result", "onmessage", "event", "onerror", "close", "onopen", "setTimeout", "formData", "FormData", "append", "post"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/src/services/apiService.js"], "sourcesContent": ["import axios from 'axios'\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: 'http://localhost:8001',\n  timeout: 300000, // 5分钟超时，适应流式响应\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n\n// 请求拦截器\napi.interceptors.request.use(\n  config => {\n    console.log('发送请求:', config)\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\napi.interceptors.response.use(\n  response => {\n    console.log('收到响应:', response)\n    return response\n  },\n  error => {\n    console.error('请求错误:', error)\n    return Promise.reject(error)\n  }\n)\n\nclass ApiService {\n  /**\n   * 发送消息到后端\n   * @param {string} apiType - API类型\n   * @param {string} message - 消息内容\n   * @param {File} file - 文件（可选）\n   * @returns {Promise<string>} 响应内容\n   */\n  async sendMessage(apiType, message, file = null) {\n    try {\n      switch (apiType) {\n        case 'simple':\n          return await this.simpleChat(message)\n        case 'substrate':\n          return await this.substrateChat(message)\n        case 'assistant':\n          return await this.assistantChat(message)\n        case 'assistantSystem':\n          return await this.assistantSystemChat(message)\n        case 'assistantStream':\n          return await this.assistantStreamChat(message)\n        case 'img2text':\n          return await this.img2text(message, file)\n        case 'img2text2':\n          return await this.img2textStream(message, file)\n        default:\n          throw new Error('未知的API类型')\n      }\n    } catch (error) {\n      console.error('API调用失败:', error)\n      throw new Error(error.response?.data?.message || error.message || '请求失败')\n    }\n  }\n\n  /**\n   * 简单聊天\n   */\n  async simpleChat(message) {\n    const response = await api.get(`/chat/simple/demo/${encodeURIComponent(message)}`)\n    return response.data\n  }\n\n  /**\n   * 底层聊天\n   */\n  async substrateChat(message) {\n    const response = await api.get(`/chat/substrate/demo/${encodeURIComponent(message)}`)\n    return response.data\n  }\n\n  /**\n   * AI助手聊天\n   */\n  async assistantChat(message) {\n    const response = await api.get(`/chat/assistant/demo/${encodeURIComponent(message)}`)\n    return response.data\n  }\n\n  /**\n   * 系统助手聊天\n   */\n  async assistantSystemChat(message) {\n    const response = await api.get(`/chat/assistantSystem/demo/${encodeURIComponent(message)}`)\n    return response.data\n  }\n\n  /**\n   * 流式助手聊天\n   */\n  async assistantStreamChat(message) {\n    return new Promise((resolve, reject) => {\n      const eventSource = new EventSource(`/api/chat/assistantStream/demo/${encodeURIComponent(message)}`)\n      let result = ''\n\n      eventSource.onmessage = function(event) {\n        if (event.data) {\n          result += event.data\n        }\n      }\n\n      eventSource.onerror = function(error) {\n        console.error('SSE错误:', error)\n        eventSource.close()\n        if (result) {\n          resolve(result)\n        } else {\n          reject(new Error('流式请求失败'))\n        }\n      }\n\n      eventSource.onopen = function() {\n        console.log('SSE连接已建立')\n      }\n\n      // 设置超时\n      setTimeout(() => {\n        eventSource.close()\n        if (result) {\n          resolve(result)\n        } else {\n          reject(new Error('请求超时'))\n        }\n      }, 300000) // 5分钟超时\n    })\n  }\n\n  /**\n   * 图片转文本\n   */\n  async img2text(message, file) {\n    if (!file) {\n      throw new Error('请选择图片文件')\n    }\n\n    const formData = new FormData()\n    formData.append('msg', message)\n    formData.append('file', file)\n\n    const response = await api.post('/multi/img2text', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    })\n    return response.data\n  }\n\n  /**\n   * 流式图片转文本\n   */\n  async img2textStream(message, file) {\n    if (!file) {\n      throw new Error('请选择图片文件')\n    }\n\n    const formData = new FormData()\n    formData.append('msg', message)\n    formData.append('file', file)\n\n    const response = await api.post('/multi/img2text2', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    })\n    return response.data\n  }\n}\n\nexport default new ApiService()\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAE,uBAAuB;EAChCC,OAAO,EAAE,MAAM;EAAE;EACjBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BC,MAAM,IAAI;EACRC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;EAC5B,OAAOA,MAAM;AACf,CAAC,EACDG,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAX,GAAG,CAACK,YAAY,CAACS,QAAQ,CAACP,GAAG,CAC3BO,QAAQ,IAAI;EACVL,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEI,QAAQ,CAAC;EAC9B,OAAOA,QAAQ;AACjB,CAAC,EACDH,KAAK,IAAI;EACPF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAC7B,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMI,UAAU,CAAC;EACf;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAEC,IAAI,GAAG,IAAI,EAAE;IAC/C,IAAI;MACF,QAAQF,OAAO;QACb,KAAK,QAAQ;UACX,OAAO,MAAM,IAAI,CAACG,UAAU,CAACF,OAAO,CAAC;QACvC,KAAK,WAAW;UACd,OAAO,MAAM,IAAI,CAACG,aAAa,CAACH,OAAO,CAAC;QAC1C,KAAK,WAAW;UACd,OAAO,MAAM,IAAI,CAACI,aAAa,CAACJ,OAAO,CAAC;QAC1C,KAAK,iBAAiB;UACpB,OAAO,MAAM,IAAI,CAACK,mBAAmB,CAACL,OAAO,CAAC;QAChD,KAAK,iBAAiB;UACpB,OAAO,MAAM,IAAI,CAACM,mBAAmB,CAACN,OAAO,CAAC;QAChD,KAAK,UAAU;UACb,OAAO,MAAM,IAAI,CAACO,QAAQ,CAACP,OAAO,EAAEC,IAAI,CAAC;QAC3C,KAAK,WAAW;UACd,OAAO,MAAM,IAAI,CAACO,cAAc,CAACR,OAAO,EAAEC,IAAI,CAAC;QACjD;UACE,MAAM,IAAIQ,KAAK,CAAC,UAAU,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,MAAM,IAAIgB,KAAK,CAAChB,KAAK,CAACG,QAAQ,EAAEc,IAAI,EAAEV,OAAO,IAAIP,KAAK,CAACO,OAAO,IAAI,MAAM,CAAC;IAC3E;EACF;;EAEA;AACF;AACA;EACE,MAAME,UAAUA,CAACF,OAAO,EAAE;IACxB,MAAMJ,QAAQ,GAAG,MAAMd,GAAG,CAAC6B,GAAG,CAAC,qBAAqBC,kBAAkB,CAACZ,OAAO,CAAC,EAAE,CAAC;IAClF,OAAOJ,QAAQ,CAACc,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMP,aAAaA,CAACH,OAAO,EAAE;IAC3B,MAAMJ,QAAQ,GAAG,MAAMd,GAAG,CAAC6B,GAAG,CAAC,wBAAwBC,kBAAkB,CAACZ,OAAO,CAAC,EAAE,CAAC;IACrF,OAAOJ,QAAQ,CAACc,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMN,aAAaA,CAACJ,OAAO,EAAE;IAC3B,MAAMJ,QAAQ,GAAG,MAAMd,GAAG,CAAC6B,GAAG,CAAC,wBAAwBC,kBAAkB,CAACZ,OAAO,CAAC,EAAE,CAAC;IACrF,OAAOJ,QAAQ,CAACc,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAML,mBAAmBA,CAACL,OAAO,EAAE;IACjC,MAAMJ,QAAQ,GAAG,MAAMd,GAAG,CAAC6B,GAAG,CAAC,8BAA8BC,kBAAkB,CAACZ,OAAO,CAAC,EAAE,CAAC;IAC3F,OAAOJ,QAAQ,CAACc,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMJ,mBAAmBA,CAACN,OAAO,EAAE;IACjC,OAAO,IAAIN,OAAO,CAAC,CAACmB,OAAO,EAAElB,MAAM,KAAK;MACtC,MAAMmB,WAAW,GAAG,IAAIC,WAAW,CAAC,kCAAkCH,kBAAkB,CAACZ,OAAO,CAAC,EAAE,CAAC;MACpG,IAAIgB,MAAM,GAAG,EAAE;MAEfF,WAAW,CAACG,SAAS,GAAG,UAASC,KAAK,EAAE;QACtC,IAAIA,KAAK,CAACR,IAAI,EAAE;UACdM,MAAM,IAAIE,KAAK,CAACR,IAAI;QACtB;MACF,CAAC;MAEDI,WAAW,CAACK,OAAO,GAAG,UAAS1B,KAAK,EAAE;QACpCF,OAAO,CAACE,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9BqB,WAAW,CAACM,KAAK,CAAC,CAAC;QACnB,IAAIJ,MAAM,EAAE;UACVH,OAAO,CAACG,MAAM,CAAC;QACjB,CAAC,MAAM;UACLrB,MAAM,CAAC,IAAIc,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B;MACF,CAAC;MAEDK,WAAW,CAACO,MAAM,GAAG,YAAW;QAC9B9B,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB,CAAC;;MAED;MACA8B,UAAU,CAAC,MAAM;QACfR,WAAW,CAACM,KAAK,CAAC,CAAC;QACnB,IAAIJ,MAAM,EAAE;UACVH,OAAO,CAACG,MAAM,CAAC;QACjB,CAAC,MAAM;UACLrB,MAAM,CAAC,IAAIc,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3B;MACF,CAAC,EAAE,MAAM,CAAC,EAAC;IACb,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMF,QAAQA,CAACP,OAAO,EAAEC,IAAI,EAAE;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAIQ,KAAK,CAAC,SAAS,CAAC;IAC5B;IAEA,MAAMc,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEzB,OAAO,CAAC;IAC/BuB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExB,IAAI,CAAC;IAE7B,MAAML,QAAQ,GAAG,MAAMd,GAAG,CAAC4C,IAAI,CAAC,iBAAiB,EAAEH,QAAQ,EAAE;MAC3DrC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOU,QAAQ,CAACc,IAAI;EACtB;;EAEA;AACF;AACA;EACE,MAAMF,cAAcA,CAACR,OAAO,EAAEC,IAAI,EAAE;IAClC,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAIQ,KAAK,CAAC,SAAS,CAAC;IAC5B;IAEA,MAAMc,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEzB,OAAO,CAAC;IAC/BuB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExB,IAAI,CAAC;IAE7B,MAAML,QAAQ,GAAG,MAAMd,GAAG,CAAC4C,IAAI,CAAC,kBAAkB,EAAEH,QAAQ,EAAE;MAC5DrC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOU,QAAQ,CAACc,IAAI;EACtB;AACF;AAEA,eAAe,IAAIb,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}