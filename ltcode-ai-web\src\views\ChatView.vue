<template>
  <div class="chat-container">
    <!-- 头部 -->
    <div class="chat-header">
      <h1>LTCode AI Chat</h1>
      <p>智能对话助手</p>
    </div>

    <!-- 消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <div 
        v-for="(message, index) in messages" 
        :key="index" 
        :class="['message-item', message.type]"
      >
        <div class="message-content">
          <div v-if="message.type === 'user' && message.file" class="file-info">
            <i class="el-icon-picture"></i>
            {{ message.file.name }}
          </div>
          <div v-html="formatMessage(message.content)"></div>
          <div v-if="message.loading" class="loading">
            <i class="el-icon-loading"></i> 正在思考...
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-area">
      <!-- API选择器 -->
      <div class="api-selector">
        <el-select 
          v-model="selectedApi" 
          placeholder="选择API接口"
          size="small"
          @change="onApiChange"
        >
          <el-option-group label="文本聊天接口">
            <el-option
              v-for="api in textApis"
              :key="api.value"
              :label="api.label"
              :value="api.value"
            />
          </el-option-group>
          <el-option-group label="多模态接口">
            <el-option
              v-for="api in multimodalApis"
              :key="api.value"
              :label="api.label"
              :value="api.value"
            />
          </el-option-group>
        </el-select>
      </div>

      <!-- 文件上传 -->
      <div v-if="needsFile" class="file-upload">
        <el-upload
          ref="upload"
          :auto-upload="false"
          :show-file-list="true"
          :on-change="handleFileChange"
          :before-remove="handleFileRemove"
          accept="image/*"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将图片拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过10MB</div>
        </el-upload>
      </div>

      <!-- 输入框和发送按钮 -->
      <div class="input-container">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="3"
          placeholder="请输入您的消息... (Enter发送，Shift+Enter换行)"
          @keydown.enter.exact="handleEnterKey"
          @keydown.enter.shift="handleShiftEnter"
          @keydown.enter.ctrl="sendMessage"
          :disabled="loading"
        />
        <el-button 
          type="primary" 
          @click="sendMessage"
          :loading="loading"
          :disabled="!canSend"
          size="large"
        >
          发送
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '@/services/apiService'

export default {
  name: 'ChatView',
  data() {
    return {
      messages: [],
      inputMessage: '',
      selectedApi: 'simple',
      loading: false,
      selectedFile: null,
      textApis: [
        { value: 'simple', label: '简单聊天' },
        { value: 'substrate', label: '底层聊天' },
        { value: 'assistant', label: 'AI助手' },
        { value: 'assistantSystem', label: '系统助手' },
        { value: 'assistantStream', label: '流式助手' }
      ],
      multimodalApis: [
        { value: 'img2text', label: '图片转文本' },
        { value: 'img2text2', label: '流式图片转文本' }
      ]
    }
  },
  computed: {
    needsFile() {
      return this.multimodalApis.some(api => api.value === this.selectedApi)
    },
    canSend() {
      const hasMessage = this.inputMessage.trim().length > 0
      const hasFileWhenNeeded = !this.needsFile || this.selectedFile
      return hasMessage && hasFileWhenNeeded && !this.loading
    }
  },
  methods: {
    onApiChange() {
      this.selectedFile = null
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
    },
    handleFileChange(file) {
      this.selectedFile = file.raw
    },
    handleFileRemove() {
      this.selectedFile = null
      return true
    },
    handleEnterKey(event) {
      // 阻止默认的换行行为
      event.preventDefault()
      // 发送消息
      this.sendMessage()
    },
    handleShiftEnter() {
      // 允许Shift+Enter换行，不阻止默认行为
      // 这里不需要做任何事情，让默认行为发生
    },
    async sendMessage() {
      if (!this.canSend) return

      const userMessage = {
        type: 'user',
        content: this.inputMessage,
        file: this.selectedFile,
        timestamp: new Date()
      }

      this.messages.push(userMessage)
      
      const assistantMessage = {
        type: 'assistant',
        content: '',
        loading: true,
        timestamp: new Date()
      }
      this.messages.push(assistantMessage)

      this.loading = true
      this.scrollToBottom()

      try {
        const response = await apiService.sendMessage(
          this.selectedApi,
          this.inputMessage,
          this.selectedFile
        )

        assistantMessage.loading = false
        assistantMessage.content = response

        this.inputMessage = ''
        this.selectedFile = null
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      } catch (error) {
        assistantMessage.loading = false
        assistantMessage.content = `错误: ${error.message}`
        this.$message.error('发送消息失败')
      } finally {
        this.loading = false
        this.scrollToBottom()
      }
    },
    formatMessage(content) {
      return content.replace(/\n/g, '<br>')
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer
        container.scrollTop = container.scrollHeight
      })
    }
  },
  mounted() {
    // 添加欢迎消息
    this.messages.push({
      type: 'assistant',
      content: '您好！我是LTCode AI助手，请选择合适的接口开始对话。',
      timestamp: new Date()
    })
  }
}
</script>

<style scoped>
.loading {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.file-info {
  margin-bottom: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-size: 12px;
}

.file-info i {
  margin-right: 5px;
}
</style>
