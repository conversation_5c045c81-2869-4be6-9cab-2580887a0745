{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as util from '../util';\nvar ENUM = 'enum';\n\n/**\n *  Rule for validating a value exists in an enumerable list.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction enumerable(rule, value, source, errors, options) {\n  rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : [];\n  if (rule[ENUM].indexOf(value) === -1) {\n    errors.push(util.format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')));\n  }\n}\nexport default enumerable;", "map": {"version": 3, "names": ["util", "ENUM", "enumerable", "rule", "value", "source", "errors", "options", "Array", "isArray", "indexOf", "push", "format", "messages", "fullField", "join"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/async-validator/es/rule/enum.js"], "sourcesContent": ["import * as util from '../util';\nvar ENUM = 'enum';\n\n/**\n *  Rule for validating a value exists in an enumerable list.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction enumerable(rule, value, source, errors, options) {\n  rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : [];\n  if (rule[ENUM].indexOf(value) === -1) {\n    errors.push(util.format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')));\n  }\n}\n\nexport default enumerable;"], "mappings": ";AAAA,OAAO,KAAKA,IAAI,MAAM,SAAS;AAC/B,IAAIC,IAAI,GAAG,MAAM;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACxDJ,IAAI,CAACF,IAAI,CAAC,GAAGO,KAAK,CAACC,OAAO,CAACN,IAAI,CAACF,IAAI,CAAC,CAAC,GAAGE,IAAI,CAACF,IAAI,CAAC,GAAG,EAAE;EACxD,IAAIE,IAAI,CAACF,IAAI,CAAC,CAACS,OAAO,CAACN,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCE,MAAM,CAACK,IAAI,CAACX,IAAI,CAACY,MAAM,CAACL,OAAO,CAACM,QAAQ,CAACZ,IAAI,CAAC,EAAEE,IAAI,CAACW,SAAS,EAAEX,IAAI,CAACF,IAAI,CAAC,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACzF;AACF;AAEA,eAAeb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}