{"ast": null, "code": "/**\n * Copyright 2013-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule isEventSupported\n */\n\n'use strict';\n\nvar ExecutionEnvironment = require('./ExecutionEnvironment');\nvar useHasFeature;\nif (ExecutionEnvironment.canUseDOM) {\n  useHasFeature = document.implementation && document.implementation.hasFeature &&\n  // always returns true in newer browsers as per the standard.\n  // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n  document.implementation.hasFeature('', '') !== true;\n}\n\n/**\n * Checks if an event is supported in the current execution environment.\n *\n * NOTE: This will not work correctly for non-generic events such as `change`,\n * `reset`, `load`, `error`, and `select`.\n *\n * Borrows from Modernizr.\n *\n * @param {string} eventNameSuffix Event name, e.g. \"click\".\n * @param {?boolean} capture Check if the capture phase is supported.\n * @return {boolean} True if the event is supported.\n * @internal\n * @license Modernizr 3.0.0pre (Custom Build) | MIT\n */\nfunction isEventSupported(eventNameSuffix, capture) {\n  if (!ExecutionEnvironment.canUseDOM || capture && !('addEventListener' in document)) {\n    return false;\n  }\n  var eventName = 'on' + eventNameSuffix;\n  var isSupported = eventName in document;\n  if (!isSupported) {\n    var element = document.createElement('div');\n    element.setAttribute(eventName, 'return;');\n    isSupported = typeof element[eventName] === 'function';\n  }\n  if (!isSupported && useHasFeature && eventNameSuffix === 'wheel') {\n    // This is the only way to test support for the `wheel` event in IE9+.\n    isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n  }\n  return isSupported;\n}\nmodule.exports = isEventSupported;", "map": {"version": 3, "names": ["ExecutionEnvironment", "require", "useHasFeature", "canUseDOM", "document", "implementation", "hasFeature", "isEventSupported", "eventNameSuffix", "capture", "eventName", "isSupported", "element", "createElement", "setAttribute", "module", "exports"], "sources": ["D:/space/workspace/ltcode_ai_module/ltcode-ai-web/node_modules/normalize-wheel/src/isEventSupported.js"], "sourcesContent": ["/**\n * Copyright 2013-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule isEventSupported\n */\n\n'use strict';\n\nvar ExecutionEnvironment = require('./ExecutionEnvironment');\n\nvar useHasFeature;\nif (ExecutionEnvironment.canUseDOM) {\n  useHasFeature =\n    document.implementation &&\n    document.implementation.hasFeature &&\n    // always returns true in newer browsers as per the standard.\n    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n    document.implementation.hasFeature('', '') !== true;\n}\n\n/**\n * Checks if an event is supported in the current execution environment.\n *\n * NOTE: This will not work correctly for non-generic events such as `change`,\n * `reset`, `load`, `error`, and `select`.\n *\n * Borrows from Modernizr.\n *\n * @param {string} eventNameSuffix Event name, e.g. \"click\".\n * @param {?boolean} capture Check if the capture phase is supported.\n * @return {boolean} True if the event is supported.\n * @internal\n * @license Modernizr 3.0.0pre (Custom Build) | MIT\n */\nfunction isEventSupported(eventNameSuffix, capture) {\n  if (!ExecutionEnvironment.canUseDOM ||\n      capture && !('addEventListener' in document)) {\n    return false;\n  }\n\n  var eventName = 'on' + eventNameSuffix;\n  var isSupported = eventName in document;\n\n  if (!isSupported) {\n    var element = document.createElement('div');\n    element.setAttribute(eventName, 'return;');\n    isSupported = typeof element[eventName] === 'function';\n  }\n\n  if (!isSupported && useHasFeature && eventNameSuffix === 'wheel') {\n    // This is the only way to test support for the `wheel` event in IE9+.\n    isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n  }\n\n  return isSupported;\n}\n\nmodule.exports = isEventSupported;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,oBAAoB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAE5D,IAAIC,aAAa;AACjB,IAAIF,oBAAoB,CAACG,SAAS,EAAE;EAClCD,aAAa,GACXE,QAAQ,CAACC,cAAc,IACvBD,QAAQ,CAACC,cAAc,CAACC,UAAU;EAClC;EACA;EACAF,QAAQ,CAACC,cAAc,CAACC,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,eAAe,EAAEC,OAAO,EAAE;EAClD,IAAI,CAACT,oBAAoB,CAACG,SAAS,IAC/BM,OAAO,IAAI,EAAE,kBAAkB,IAAIL,QAAQ,CAAC,EAAE;IAChD,OAAO,KAAK;EACd;EAEA,IAAIM,SAAS,GAAG,IAAI,GAAGF,eAAe;EACtC,IAAIG,WAAW,GAAGD,SAAS,IAAIN,QAAQ;EAEvC,IAAI,CAACO,WAAW,EAAE;IAChB,IAAIC,OAAO,GAAGR,QAAQ,CAACS,aAAa,CAAC,KAAK,CAAC;IAC3CD,OAAO,CAACE,YAAY,CAACJ,SAAS,EAAE,SAAS,CAAC;IAC1CC,WAAW,GAAG,OAAOC,OAAO,CAACF,SAAS,CAAC,KAAK,UAAU;EACxD;EAEA,IAAI,CAACC,WAAW,IAAIT,aAAa,IAAIM,eAAe,KAAK,OAAO,EAAE;IAChE;IACAG,WAAW,GAAGP,QAAQ,CAACC,cAAc,CAACC,UAAU,CAAC,cAAc,EAAE,KAAK,CAAC;EACzE;EAEA,OAAOK,WAAW;AACpB;AAEAI,MAAM,CAACC,OAAO,GAAGT,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}